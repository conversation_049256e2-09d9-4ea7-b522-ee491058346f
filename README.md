# Auth-Clear Payment Processing Platform

A modern, serverless payment processing platform built on AWS, providing secure merchant onboarding and PCI-compliant payment processing through Payrix integration.

## 🚀 Quick Start

```bash
# Prerequisites: AWS CLI configured with 'payrix' profile

# 1. Generate environment variables (required after infrastructure changes)
npm run generate-env

# 2. Install dependencies
npm install
cd frontend && npm install
cd ../functions && npm install

# 3. Start development servers
npm run start:frontend  # Frontend: http://localhost:5173
npm run offline        # API: http://localhost:3000
```

## 🏗️ Architecture Overview

Auth-Clear uses a **multi-stack serverless architecture** deployed on AWS:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│ Infrastructure  │     │    Frontend     │     │    Functions    │
│     Stack       │     │     Stack       │     │     Stack       │
├─────────────────┤     ├─────────────────┤     ├─────────────────┤
│ • VPC & Subnets │     │ • React App     │     │ • Lambda APIs   │
│ • NAT Gateway   │     │ • S3 Hosting    │     │ • API Gateway   │
│ • Security Groups│     │ • CloudFront    │     │ • DynamoDB      │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

### Key AWS Services

- **VPC**: Isolated network with public/private subnets
- **Lambda**: Serverless compute for API endpoints
- **DynamoDB**: NoSQL database for payment tokens (with TTL)
- **API Gateway**: REST API management
- **CloudFront**: CDN for frontend distribution
- **S3**: Static website hosting

## 💻 Technology Stack

### Backend
- **Runtime**: Node.js 20 with TypeScript
- **Framework**: Serverless Framework v4
- **Validation**: Zod schemas
- **HTTP Client**: Axios
- **Database**: DynamoDB with automatic TTL

### Frontend
- **Framework**: React 19 (with automatic JSX transform)
- **Build Tool**: Vite
- **State Management**: Redux Toolkit + redux-persist
- **Routing**: React Router v7
- **Styling**: Tailwind CSS v4
- **UI Feedback**: Sonner toast notifications

## 🔌 API Endpoints

### Merchant Endpoints (Restricted CORS)
| Method | Endpoint | Purpose |
|--------|----------|---------|
| POST | `/merchants/onboard` | Complete merchant onboarding with Payrix |

### Payment Endpoints (Open CORS for iframe embedding)
| Method | Endpoint | Purpose |
|--------|----------|---------|
| POST | `/payments/generate-payment-config` | Generate payment configuration |
| POST | `/payments/generate-integration-token` | Create secure payment tokens |
| POST | `/payments/validate-iframe-token` | Validate tokens for iframe |
| GET/POST | `/payments/iframe-config` | Get iframe styling configuration |
| GET/POST | `/payments/token-status` | Check token validity |
| POST | `/payments/process-token-payment` | Process the actual payment |

## 💳 Payment Flow

```
1. Merchant generates token → Stored in DynamoDB
2. Customer loads payment page → Iframe embedded
3. Iframe validates token → Loads PayFields SDK
4. Customer enters card → Secure fields (PCI compliant)
5. Payment processed → Returns to merchant site
```

### PCI Compliance
- Card data never touches our servers
- Payrix PayFields handles all sensitive data
- Iframe isolation for security
- Token-based transaction flow

## 📁 Project Structure

```
auth-clear/
├── infra/                 # Infrastructure stack (VPC, networking)
├── frontend/              # React application
│   ├── src/
│   │   ├── components/    # Reusable UI components
│   │   ├── pages/         # Route pages
│   │   ├── redux/         # State management
│   │   ├── services/      # API service layer
│   │   └── types/         # TypeScript types
│   └── serverless.yml     # Frontend deployment config
├── functions/             # Lambda functions
│   ├── src/
│   │   ├── functions/     # Individual Lambda handlers
│   │   ├── service/       # Business logic services
│   │   ├── middleware/    # Security middleware
│   │   └── types/         # Shared types
│   └── serverless.yml     # API deployment config
└── package.json           # Root package scripts
```

## 🚀 Deployment

Always deploy in this order:

```bash
# 1. Infrastructure (if changed)
npm run deploy:infra

# 2. Frontend
npm run deploy:frontend

# 3. Functions
npm run deploy:functions

# To remove everything (reverse order)
npm run remove:all
```

### Deployment Commands
- `npm run deploy:all` - Deploy all stacks in correct order
- `npm run deploy:infra` - Deploy infrastructure only
- `npm run deploy:frontend` - Deploy frontend only
- `npm run deploy:functions` - Deploy Lambda functions only

## 🔧 Development Guidelines

### Code Standards
1. **File Size**: Keep all files under 250 lines
2. **Comments**: Write self-documenting code, avoid unnecessary comments
3. **Error Handling**: Use try/catch for all async operations
4. **Validation**: Use Zod schemas for all inputs
5. **Security**: Never log sensitive data (tokens, keys, PII)

### Testing
```bash
# Run linting
npm run lint

# View Lambda logs
npm run logs:create-merchant
npm run logs:list-merchants
```

## 🔐 Security Features

- **Token Format**: 64-character hex strings
- **Token Expiration**: Automatic TTL in DynamoDB
- **Single-Use Tokens**: Marked as used after payment
- **Input Validation**: Comprehensive Zod schemas
- **Amount Limits**: $0.50 - $100,000
- **HTTPS Required**: For all return URLs
- **CORS Strategy**: Restricted for merchant APIs, open for payment iframes

## 📚 Key Features

- **Multi-Owner Support**: Merchants can have multiple owners
- **MCC Codes**: Integrated Merchant Category Codes
- **Automated Validation**: EIN/SSN format checking
- **Duplicate Prevention**: Check for existing merchants
- **Responsive Design**: Mobile-first approach
- **Persistent State**: Form data survives page refreshes

## 🛠️ Troubleshooting

### Environment Variables Missing
```bash
npm run generate-env
```

### CORS Errors
- Verify endpoint is in the correct CORS configuration
- Payment endpoints should allow all origins

### Lambda Function Errors
```bash
# View specific function logs
npm run logs:create-merchant
```

### Build Failures
```bash
# Clear caches and rebuild
rm -rf node_modules package-lock.json
npm install
```


- **Payrix API**: https://resource.payrix.com/docs