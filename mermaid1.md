flowchart TD
    %% Merchant Onboarding Flow
    A[New Merchant] --> B[Visit Homepage]
    B --> C[Click Onboarding]
    C --> D[Business Info Form]
    D --> E{Form Validation}
    E -->|Invalid| D
    E -->|Valid| F[Submit to Backend]
    F --> G[Validate with Payrix API]
    G --> H{Merchant Exists?}
    H -->|Yes| I[Return Error]
    H -->|No| J[Create Payrix Entity]
    J --> K{User Account Requested?}
    K -->|Yes| L[Create Payrix User Account]
    K -->|No| M[Onboarding Success]
    L --> M
    M --> N[Redirect to Success Page]
    I --> O[Show Error Message]

    %% Simple Payment Processing Flow
    P[Customer] --> Q[Visit Payment Page]
    Q --> R[Generate Integration Token]
    R --> S[Validate Merchant ID]
    S --> T{Merchant Valid?}
    T -->|No| U[Show Error]
    T -->|Yes| V[Create Secure Token]
    V --> W[Store Token in DynamoDB]
    W --> X[Generate Embed URL]
    X --> Y[Load Payment Iframe]
    Y --> Z[Customer Fills Payment Form]
    Z --> AA[Enter Billing Address]
    AA --> BB[Accept Terms]
    BB --> CC[Submit Payment]
    CC --> DD[PayFields Tokenization]
    DD --> EE{Token Created?}
    EE -->|No| FF[Show Payment Error]
    EE -->|Yes| GG[Process Token Payment]
    GG --> HH[Send to Payrix Transaction API]
    HH --> II{Payment Success?}
    II -->|No| JJ[Show Transaction Error]
    II -->|Yes| KK[Delete Token]
    KK --> LL[Show Success Message]
    FF --> MM[Return to Payment Form]
    JJ --> MM

    %% Developer Integration Workflow
    NN[Developer] --> OO[Visit Iframe Demo]
    OO --> PP[Configure Payment Settings]
    PP --> QQ[Generate Integration Token]
    QQ --> RR[View Live Iframe Preview]
    RR --> SS[Monitor Event Log]
    SS --> TT[Test Payment Flow]
    TT --> UU[Copy Integration URL]
    UU --> VV[Implement in Application]

    %% Error Handling and Edge Cases
    WW[Token Expiry] --> XX[Validate Token Status]
    XX --> YY{Token Valid?}
    YY -->|Expired| ZZ[Show Expiry Error]
    YY -->|Used| AAA[Show Used Error]
    YY -->|Invalid| BBB[Show Invalid Error]
    
    CCC[Network Error] --> DDD[Retry Mechanism]
    DDD --> EEE{Max Retries?}
    EEE -->|No| CCC
    EEE -->|Yes| FFF[Show Network Error]
    
    GGG[Validation Error] --> HHH[Show Field Errors]
    HHH --> III[User Corrects Input]
    III --> JJJ[Resubmit Form]

    %% Styling
    classDef merchantFlow fill:#e1f5fe
    classDef paymentFlow fill:#f3e5f5
    classDef developerFlow fill:#e8f5e8
    classDef errorFlow fill:#ffebee
    
    class A,B,C,D,E,F,G,H,I,J,K,L,M,N,O merchantFlow
    class P,Q,R,S,T,U,V,W,X,Y,Z,AA,BB,CC,DD,EE,FF,GG,HH,II,JJ,KK,LL,MM paymentFlow
    class NN,OO,PP,QQ,RR,SS,TT,UU,VV developerFlow
    class WW,XX,YY,ZZ,AAA,BBB,CCC,DDD,EEE,FFF,GGG,HHH,III,JJJ errorFlow