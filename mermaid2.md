graph TB
    %% Frontend Layer
    subgraph "Frontend (React + TypeScript)"
        subgraph "Pages"
            HomePage[Home Page]
            OnboardingPage[Onboarding Page]
            PaymentPage[Payment Page]
            PaymentIframe[Payment Iframe]
            IframeDemoPage[Iframe Demo Page]
            IframeDocsPage[Integration Docs]
        end
        
        subgraph "Components"
            Navigation[Navigation]
            PaymentForm[Payment Form Components]
            BillingForm[Billing Address Form]
            PayFieldsIntegration[PayFields Integration]
            EventLog[Event Log]
            LoadingStates[Loading/Error States]
        end
        
        subgraph "Services & State"
            ApiClient[API Client - Axios]
            ReduxStore[Redux Store]
            PaymentHooks[Payment Hooks]
            IframeHooks[Iframe Hooks]
            ValidationUtils[Validation Utils]
        end
    end

    %% Backend Layer
    subgraph "Backend (AWS Lambda + Node.js)"
        subgraph "API Gateway Endpoints"
            MerchantAPI["/merchants/onboard"]
            PaymentConfigAPI["/payments/generate-payment-config"]
            TokenGenAPI["/payments/generate-integration-token"]
            TokenValidateAPI["/payments/validate-iframe-token"]
            TokenStatusAPI["/payments/token-status"]
            ProcessPaymentAPI["/payments/process-token-payment"]
        end
        
        subgraph "Lambda Functions"
            OnboardLambda[Merchant Onboard Handler]
            TokenGenLambda[Integration Token Generator]
            TokenValidateLambda[Token Validator]
            PaymentProcessLambda[Payment Processor]
        end
        
        subgraph "Service Layer"
            PayrixService[Payrix Service]
            MerchantService[Merchant Service]
            UserService[User Service]
            PaymentService[Payment Service]
            TokenStorageService[Token Storage Service]
        end
        
        subgraph "Middleware & Security"
            SecurityMiddleware[Security Middleware]
            CORSHandler[CORS Handler]
            InputValidation[Input Validation]
            TokenValidation[Token Format Validation]
        end
    end

    %% Database Layer
    subgraph "Data Storage"
        DynamoDB[(DynamoDB)]
        PaymentTokensTable[Payment Tokens Table]
        TTLConfig[TTL Configuration]
    end

    %% External Services
    subgraph "External Integrations"
        PayrixAPI[Payrix API]
        PayrixEntities["/entities - Merchant Creation"]
        PayrixLogins["/logins - User Accounts"]
        PayrixTransactions["/txns - Payment Processing"]
        PayrixTokens["/tokens - Token Management"]
        PayFieldsScript[PayFields JavaScript SDK]
    end

    %% AWS Infrastructure
    subgraph "AWS Infrastructure"
        CloudFront[CloudFront Distribution]
        S3Bucket[S3 Static Hosting]
        APIGateway[API Gateway]
        LambdaRuntime[Lambda Runtime]
        IAMRoles[IAM Roles & Policies]
        VPC[VPC Configuration]
    end

    %% Data Flow Connections
    HomePage --> Navigation
    OnboardingPage --> MerchantAPI
    PaymentPage --> TokenGenAPI
    PaymentIframe --> PayFieldsIntegration
    IframeDemoPage --> TokenGenAPI
    
    PaymentForm --> PayFieldsScript
    PayFieldsIntegration --> ProcessPaymentAPI
    
    ApiClient --> APIGateway
    ReduxStore --> PaymentHooks
    PaymentHooks --> IframeHooks
    
    MerchantAPI --> OnboardLambda
    TokenGenAPI --> TokenGenLambda
    ProcessPaymentAPI --> PaymentProcessLambda
    
    OnboardLambda --> PayrixService
    TokenGenLambda --> PayrixService
    PaymentProcessLambda --> PayrixService
    
    PayrixService --> MerchantService
    PayrixService --> UserService
    PayrixService --> PaymentService
    
    MerchantService --> PayrixEntities
    UserService --> PayrixLogins
    PaymentService --> PayrixTransactions
    PaymentService --> PayrixTokens
    
    TokenGenLambda --> TokenStorageService
    TokenValidateLambda --> TokenStorageService
    TokenStorageService --> DynamoDB
    DynamoDB --> PaymentTokensTable
    PaymentTokensTable --> TTLConfig
    
    SecurityMiddleware --> CORSHandler
    SecurityMiddleware --> InputValidation
    SecurityMiddleware --> TokenValidation
    
    CloudFront --> S3Bucket
    S3Bucket --> HomePage
    APIGateway --> LambdaRuntime
    LambdaRuntime --> IAMRoles
    
    %% Authentication & Authorization Flow
    subgraph "Security & Validation"
        TokenGeneration[Secure Token Generation]
        MerchantValidation[Merchant ID Validation]
        InputSanitization[Input Sanitization]
        SecurityHeaders[Security Headers]
        CSPPolicy[Content Security Policy]
    end
    
    TokenGeneration --> TokenStorageService
    MerchantValidation --> PayrixAPI
    InputSanitization --> SecurityMiddleware
    SecurityHeaders --> SecurityMiddleware
    CSPPolicy --> SecurityMiddleware

    %% Styling
    classDef frontend fill:#e3f2fd
    classDef backend fill:#f3e5f5
    classDef database fill:#e8f5e8
    classDef external fill:#fff3e0
    classDef aws fill:#ffebee
    classDef security fill:#f1f8e9
    
    class HomePage,OnboardingPage,PaymentPage,PaymentIframe,IframeDemoPage,IframeDocsPage,Navigation,PaymentForm,BillingForm,PayFieldsIntegration,EventLog,LoadingStates,ApiClient,ReduxStore,PaymentHooks,IframeHooks,ValidationUtils frontend
    
    class MerchantAPI,PaymentConfigAPI,TokenGenAPI,TokenValidateAPI,TokenStatusAPI,ProcessPaymentAPI,OnboardLambda,TokenGenLambda,TokenValidateLambda,PaymentProcessLambda,PayrixService,MerchantService,UserService,PaymentService,TokenStorageService,SecurityMiddleware,CORSHandler,InputValidation,TokenValidation backend
    
    class DynamoDB,PaymentTokensTable,TTLConfig database
    
    class PayrixAPI,PayrixEntities,PayrixLogins,PayrixTransactions,PayrixTokens,PayFieldsScript external
    
    class CloudFront,S3Bucket,APIGateway,LambdaRuntime,IAMRoles,VPC aws
    
    class TokenGeneration,MerchantValidation,InputSanitization,SecurityHeaders,CSPPolicy security