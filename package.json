{"name": "auth-clear", "version": "1.0.0", "description": "auth-clear", "main": "handler.js", "type": "module", "scripts": {"generate-env": "./scripts/generate-env.sh dev payrix", "lint": "eslint . --ext .ts", "deploy:infra": "cd infra && serverless deploy --stage dev --aws-profile payrix", "deploy:functions": "cd functions && serverless deploy --stage dev --aws-profile payrix", "deploy:frontend": "cd frontend && serverless deploy --stage dev --aws-profile payrix && npm run update-cloudfront-id && npm run deploy", "deploy:frontend:files": "cd frontend && npm run deploy", "start:frontend": "cd frontend && npm run dev", "deploy:all": "npm run deploy:infra && npm run deploy:frontend && npm run deploy:functions", "remove:functions": "cd functions && serverless remove --stage dev --aws-profile payrix", "remove:infra": "cd infra && serverless remove --stage dev --aws-profile payrix", "remove:frontend": "cd frontend && aws s3 rm s3://auth-clear-frontend-dev --recursive --profile payrix && serverless remove --stage dev --aws-profile payrix", "remove:all": "npm run remove:functions && npm run remove:frontend && npm run remove:infra", "deploy": "npm run deploy:all", "remove": "npm run remove:all", "offline": "cd functions && serverless offline start --stage dev --aws-profile payrix"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-ssm": "^3.536.0", "@types/aws-lambda": "^8.10.136", "@types/node": "^22.14.0", "aws-lambda": "^1.0.7", "aws-sdk": "^2.1692.0", "serverless-export-env": "^2.2.0", "serverless-iam-roles-per-function": "^3.2.0", "serverless-offline": "^14.4.0", "tsx": "^4.19.3", "typescript": "^5.8.2"}, "devDependencies": {"@eslint/js": "^9.28.0", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "esbuild": "^0.25.5", "eslint": "^9.28.0", "globals": "^16.2.0", "serverless-esbuild": "^1.55.0", "typescript-eslint": "^8.33.1"}}