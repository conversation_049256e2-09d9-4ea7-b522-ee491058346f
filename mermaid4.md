graph TB
    %% Database Schema
    subgraph "DynamoDB Schema"
        subgraph "PaymentTokens Table"
            TokenId[tokenId: String - HASH KEY]
            MerchantId[merchantId: String]
            Description[description: String]
            Amount[amount: Number]
            ReturnUrl[returnUrl: String - Optional]
            ExpiresAt[expiresAt: Date - TTL Attribute]
            Used[used: Boolean]
            Currency[currency: String - Default: USD]
            Items[items: Array - Optional]
            TaxAmount[taxAmount: Number - Optional]
            ShippingAmount[shippingAmount: Number - Optional]
            OrderNumber[orderNumber: String - Optional]
            InvoiceNumber[invoiceNumber: String - Optional]
            CustomerCode[customerCode: String - Optional]
            MerchantInfo[merchantInfo: Object - Optional]
        end
        
        TTLConfig[TTL Configuration: Auto-delete expired tokens]
        BillingMode[Billing Mode: PAY_PER_REQUEST]
        Encryption[Encryption: AWS Managed Keys]
    end

    %% Security Architecture
    subgraph "Security Layers"
        subgraph "Frontend Security"
            CSP[Content Security Policy]
            CORS[CORS Configuration]
            InputSanitization[Input Sanitization]
            XSSProtection[XSS Protection Headers]
            FrameOptions[X-Frame-Options: SAMEORIGIN]
        end
        
        subgraph "API Security"
            SecurityMiddleware[Security Middleware Wrapper]
            TokenValidation[Token Format Validation]
            MerchantValidation[Merchant ID Validation]
            RateLimiting[Rate Limiting - API Gateway]
            RequestValidation[Request Schema Validation]
        end
        
        subgraph "Token Security"
            SecureGeneration[Crypto.randomBytes - 32 bytes]
            HexEncoding[Hex Encoding - 64 characters]
            TTLExpiry[Automatic TTL Expiry]
            SingleUse[Single-use Token Pattern]
            TokenCleanup[Automatic Token Cleanup]
        end
        
        subgraph "Infrastructure Security"
            IAMRoles[IAM Roles & Policies]
            VPCConfig[VPC Configuration]
            PrivateSubnets[Private Subnets]
            SecurityGroups[Security Groups]
            SSMParameters[SSM Parameter Store]
        end
    end

    %% Data Validation Schema
    subgraph "Validation Schemas (Zod)"
        TokenRequestSchema[Token Request Schema]
        OnboardingSchema[Onboarding Schema]
        PaymentSchema[Payment Schema]
        AddressSchema[Address Schema]
        ItemSchema[Item Schema]
        
        TokenRequestSchema --> MerchantIdValidation[merchantId: required string]
        TokenRequestSchema --> DescriptionValidation[description: required string]
        TokenRequestSchema --> AmountValidation[amount: optional positive integer]
        TokenRequestSchema --> ExpiresInValidation[expiresIn: 1-1440 minutes]
        TokenRequestSchema --> CurrencyValidation[currency: 3-letter ISO code]
        
        OnboardingSchema --> BusinessValidation[Business Info Validation]
        OnboardingSchema --> EINValidation[EIN: 9 digits]
        OnboardingSchema --> EmailValidation[Email: valid format]
        OnboardingSchema --> PhoneValidation[Phone: 10 digits]
        
        PaymentSchema --> TokenFormatValidation[Token: 64-char hex]
        PaymentSchema --> CustomerInfoValidation[Customer Info: optional]
        
        AddressSchema --> AddressLineValidation[Address: no PO boxes]
        AddressSchema --> ZipValidation[ZIP: 5 or 5-4 format]
        AddressSchema --> StateValidation[State: valid US state]
        
        ItemSchema --> ItemNameValidation[name: required]
        ItemSchema --> QuantityValidation[quantity: positive integer]
        ItemSchema --> PriceValidation[unitPrice: non-negative]
    end

    %% Security Headers Configuration
    subgraph "Security Headers"
        ContentTypeOptions[X-Content-Type-Options: nosniff]
        XSSProtectionHeader[X-XSS-Protection: 1; mode=block]
        ReferrerPolicy[Referrer-Policy: strict-origin-when-cross-origin]
        CSPHeader[Content-Security-Policy: Strict policy]
        AccessControlHeaders[Access-Control-* Headers]
    end

    %% AWS Security Integration
    subgraph "AWS Security Services"
        CloudTrail[CloudTrail: API Logging]
        CloudWatch[CloudWatch: Monitoring]
        WAF[WAF: Web Application Firewall]
        KMS[KMS: Key Management]
        SecretsManager[Secrets Manager: API Keys]
    end

    %% Connections
    TokenId --> TTLConfig
    ExpiresAt --> TTLConfig
    
    SecurityMiddleware --> CSP
    SecurityMiddleware --> CORS
    SecurityMiddleware --> XSSProtection
    SecurityMiddleware --> FrameOptions
    
    TokenValidation --> SecureGeneration
    TokenValidation --> HexEncoding
    TokenValidation --> TokenFormatValidation
    
    IAMRoles --> VPCConfig
    VPCConfig --> PrivateSubnets
    PrivateSubnets --> SecurityGroups
    
    SSMParameters --> SecretsManager
    SecretsManager --> KMS
    
    SecurityMiddleware --> ContentTypeOptions
    SecurityMiddleware --> XSSProtectionHeader
    SecurityMiddleware --> ReferrerPolicyw
    SecurityMiddleware --> CSPHeader
    SecurityMiddleware --> AccessControlHeaders
    
    CloudTrail --> CloudWatch
    CloudWatch --> WAF
    
    %% Styling
    classDef database fill:#e8f5e8
    classDef security fill:#ffebee
    classDef validation fill:#f3e5f5
    classDef headers fill:#e1f5fe
    classDef aws fill:#fff3e0
    
    class TokenId,MerchantId,Description,Amount,ReturnUrl,ExpiresAt,Used,Currency,Items,TaxAmount,ShippingAmount,OrderNumber,InvoiceNumber,CustomerCode,MerchantInfo,TTLConfig,BillingMode,Encryption database
    
    class CSP,CORS,InputSanitization,XSSProtection,FrameOptions,SecurityMiddleware,TokenValidation,MerchantValidation,RateLimiting,RequestValidation,SecureGeneration,HexEncoding,TTLExpiry,SingleUse,TokenCleanup,IAMRoles,VPCConfig,PrivateSubnets,SecurityGroups,SSMParameters security
    
    class TokenRequestSchema,OnboardingSchema,PaymentSchema,AddressSchema,ItemSchema,MerchantIdValidation,DescriptionValidation,AmountValidation,ExpiresInValidation,CurrencyValidation,BusinessValidation,EINValidation,EmailValidation,PhoneValidation,TokenFormatValidation,CustomerInfoValidation,AddressLineValidation,ZipValidation,StateValidation,ItemNameValidation,QuantityValidation,PriceValidation validation
    
    class ContentTypeOptions,XSSProtectionHeader,ReferrerPolicy,CSPHeader,AccessControlHeaders headers
    
    class CloudTrail,CloudWatch,WAF,KMS,SecretsManager aws