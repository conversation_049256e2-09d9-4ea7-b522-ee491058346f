#!/bin/bash

# Configuration
STAGE=${1:-dev}
AWS_PROFILE=${2:-payrix}

# Stack name
INFRA_STACK="auth-clear-infra-${STAGE}"

echo "Generating .env file with database credentials..."
echo "Stage: ${STAGE}"
echo "AWS Profile: ${AWS_PROFILE}"

# Get DB endpoint and port
DB_ENDPOINT=$(aws cloudformation describe-stacks --stack-name ${INFRA_STACK} \
  --query "Stacks[0].Outputs[?OutputKey=='AuroraClusterEndpoint'].OutputValue" \
  --output text \
  --profile ${AWS_PROFILE})

DB_PORT=$(aws cloudformation describe-stacks --stack-name ${INFRA_STACK} \
  --query "Stacks[0].Outputs[?OutputKey=='AuroraClusterPort'].OutputValue" \
  --output text \
  --profile ${AWS_PROFILE})

# Get the Secret ARN from CloudFormation exports
SECRET_ARN=$(aws cloudformation describe-stacks --stack-name ${INFRA_STACK} \
  --query "Stacks[0].Outputs[?OutputKey=='AuroraSecretArn'].OutputValue" \
  --output text \
  --profile ${AWS_PROFILE})

if [ -z "$SECRET_ARN" ]; then
  echo "Error: Could not retrieve secret ARN from CloudFormation."
  exit 1
fi

echo "Secret ARN: ${SECRET_ARN}"

# Get database credentials
DB_CREDENTIALS=$(aws secretsmanager get-secret-value \
  --secret-id "${SECRET_ARN}" \
  --query "SecretString" \
  --output text \
  --profile ${AWS_PROFILE})

# If that fails, try direct name formats
if [ -z "$DB_CREDENTIALS" ]; then
  echo "Trying direct secret name..."
  DB_CREDENTIALS=$(aws secretsmanager get-secret-value \
    --secret-id "auth-clear-infra/${STAGE}/aurora-credentials" \
    --query "SecretString" \
    --output text \
    --profile ${AWS_PROFILE})
fi

if [ -z "$DB_CREDENTIALS" ]; then
  echo "Error: Could not retrieve database credentials from Secrets Manager."
  exit 1
fi

# Extract username and password
DB_USERNAME=$(echo $DB_CREDENTIALS | grep -o '"username":"[^"]*' | sed 's/"username":"//g')
DB_PASSWORD=$(echo $DB_CREDENTIALS | grep -o '"password":"[^"]*' | sed 's/"password":"//g')

echo "Database credentials retrieved."
echo "Creating .env file..."

# Create or overwrite .env file
# cat > .env << EOL
# # Generated by generate-env.sh on $(date)
# STAGE=${STAGE}
# DB_ENDPOINT=${DB_ENDPOINT}
# DB_HOST=localhost
# DB_PORT=${DB_PORT}
# DB_NAME=authcleardb
# DB_USERNAME=${DB_USERNAME}
# DB_PASSWORD=${DB_PASSWORD}
# EOL


cd ./functions
cat > .env << EOL
# Generated by generate-env.sh on $(date)
STAGE=${STAGE}
DB_ENDPOINT=${DB_ENDPOINT}
DB_HOST=127.0.0.1
DB_PORT=${DB_PORT}
DB_NAME=authcleardb
DB_USERNAME=${DB_USERNAME}
DB_PASSWORD=${DB_PASSWORD}
EOL

echo ".env file created successfully with actual database credentials."
