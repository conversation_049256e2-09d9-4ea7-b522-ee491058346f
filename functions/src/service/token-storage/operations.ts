import { Put<PERSON><PERSON><PERSON>, GetCommand, DeleteCommand } from "@aws-sdk/lib-dynamodb";
import { logger } from "../../helpers/logger.js";
import type { TokenData, TokenValidationResult, DynamoTokenItem } from "../../types/token-storage.types.js";
import { getDynamoClient, TABLE_NAME } from "./dynamo-client.js";
import { toDynamoItem, fromDynamoItem } from "./converters.js";

export async function setToken(token: string, data: TokenData): Promise<void> {
  try {
    const item = toDynamoItem(token, data);
    const client = getDynamoClient();

    await client.send(
      new PutCommand({
        TableName: TABLE_NAME,
        Item: item,
      })
    );
  } catch (error) {
    logger.error("Error storing token", { error });
    throw new Error(`Failed to store token: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

export async function getToken(token: string): Promise<TokenData | null> {
  try {
    const client = getDynamoClient();
    const result = await client.send(
      new GetCommand({
        TableName: TABLE_NAME,
        Key: { tokenId: token },
      })
    );

    if (!result.Item) {
      return null;
    }

    return fromDynamoItem(result.Item as DynamoTokenItem);
  } catch (error) {
    logger.error("Error retrieving token", { error });
    throw new Error(`Failed to retrieve token: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

export async function deleteToken(token: string): Promise<void> {
  try {
    const client = getDynamoClient();
    await client.send(
      new DeleteCommand({
        TableName: TABLE_NAME,
        Key: { tokenId: token },
      })
    );
  } catch (error) {
    logger.error("Error deleting token", { error });
    throw new Error(`Failed to delete token: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

export async function markTokenAsUsed(token: string): Promise<boolean> {
  try {
    const tokenData = await getToken(token);
    if (!tokenData) {
      logger.error("Token not found", { token });
      return false;
    }

    if (tokenData.used) {
      logger.error("Token already used", { token });
      return false;
    }

    if (tokenData.expiresAt <= new Date()) {
      logger.error("Token expired", { token });
      return false;
    }

    tokenData.used = true;
    await setToken(token, tokenData);
    return true;
  } catch (error) {
    logger.error("Error marking token as used", { error });
    return false;
  }
}

export async function validateToken(token: string): Promise<TokenValidationResult> {
  try {
    const tokenData = await getToken(token);

    if (!tokenData) {
      return {
        isValid: false,
        error: "Token not found",
      };
    }

    if (tokenData.used) {
      return {
        isValid: false,
        error: "Token already used",
      };
    }

    if (tokenData.expiresAt <= new Date()) {
      return {
        isValid: false,
        error: "Token expired",
      };
    }

    return {
      isValid: true,
      data: tokenData,
    };
  } catch (error) {
    logger.error("Error validating token", { error });
    return {
      isValid: false,
      error: "Token validation failed",
    };
  }
}