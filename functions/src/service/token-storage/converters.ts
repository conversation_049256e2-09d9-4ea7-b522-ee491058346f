import type { TokenData, StoredTokenData, DynamoTokenItem } from "../../types/token-storage.types.js";

export function toDynamoItem(token: string, data: TokenData): DynamoTokenItem {
  return {
    tokenId: token,
    ...data,
    expiresAt: Math.floor(data.expiresAt.getTime() / 1000),
  };
}

export function fromDynamoItem(item: DynamoTokenItem): TokenData {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { tokenId, expiresAt, ...rest } = item;
  return {
    ...rest,
    expiresAt: new Date(expiresAt * 1000),
  };
}

export function toStoredTokenData(data: TokenData): StoredTokenData {
  return {
    ...data,
    expiresAt: data.expiresAt.toISOString(),
  };
}

export function fromStoredTokenData(data: StoredTokenData): TokenData {
  return {
    ...data,
    expiresAt: new Date(data.expiresAt),
  };
}