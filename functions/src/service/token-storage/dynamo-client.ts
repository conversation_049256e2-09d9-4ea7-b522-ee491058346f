import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocumentClient } from "@aws-sdk/lib-dynamodb";

let dynamoClient: DynamoDBDocumentClient | null = null;

export const TABLE_NAME = process.env.PAYMENT_TOKENS_TABLE_NAME || "PaymentTokens-dev";

const clientConfig: { region: string; profile?: string } = {
  region: process.env.AWS_REGION || "us-east-1",
  profile: process.env.STAGE && process.env.STAGE === "dev" ? "payrix" : "default",
};

export function getDynamoClient(): DynamoDBDocumentClient {
  if (!dynamoClient) {
    const client = new DynamoDBClient({
      region: clientConfig?.region,
      profile: clientConfig?.profile,
    });
    dynamoClient = DynamoDBDocumentClient.from(client, {
      marshallOptions: {
        removeUndefinedValues: true,
      },
    });
  }
  return dynamoClient;
}