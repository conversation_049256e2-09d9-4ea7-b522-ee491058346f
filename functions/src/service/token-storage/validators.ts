import type { TokenData, TokenDataValidationResult } from "../../types/token-storage.types.js";

export const REQUIRED_FIELDS = ["merchantId", "description", "amount", "expiresAt"] as const;
export const MAX_DESCRIPTION_LENGTH = 500;
export const MAX_AMOUNT = 999999999;
export const MIN_AMOUNT = 1;

export function validateTokenData(data: Partial<TokenData>): TokenDataValidationResult {
  const errors: string[] = [];

  for (const field of REQUIRED_FIELDS) {
    if (!data[field]) {
      errors.push(`Missing required field: ${field}`);
    }
  }

  if (data.amount !== undefined) {
    if (typeof data.amount !== "number" || data.amount < MIN_AMOUNT || data.amount > MAX_AMOUNT) {
      errors.push(`Amount must be between ${MIN_AMOUNT} and ${MAX_AMOUNT} cents`);
    }
  }

  if (data.description && data.description.length > MAX_DESCRIPTION_LENGTH) {
    errors.push(`Description must be ${MAX_DESCRIPTION_LENGTH} characters or less`);
  }

  if (data.expiresAt && data.expiresAt <= new Date()) {
    errors.push("Expiration date must be in the future");
  }

  if (data.items) {
    if (!Array.isArray(data.items) || data.items.length === 0) {
      errors.push("Items must be a non-empty array");
    } else {
      data.items.forEach((item, index) => {
        if (!item.name || typeof item.name !== "string") {
          errors.push(`Item ${index + 1}: name is required`);
        }
        if (typeof item.quantity !== "number" || item.quantity <= 0) {
          errors.push(`Item ${index + 1}: quantity must be a positive number`);
        }
        if (typeof item.unitPrice !== "number" || item.unitPrice < 0) {
          errors.push(`Item ${index + 1}: unitPrice must be a non-negative number`);
        }
        if (typeof item.total !== "number" || item.total < 0) {
          errors.push(`Item ${index + 1}: total must be a non-negative number`);
        }
      });
    }
  }

  return { isValid: errors.length === 0, errors };
}