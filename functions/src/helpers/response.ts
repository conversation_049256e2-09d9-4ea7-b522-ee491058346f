import type { APIGatewayProxyResult } from "aws-lambda";

const CORS_HEADERS = {
  "Content-Type": "application/json",
  "Access-Control-Allow-Origin": "*",
} as const;

const IFRAME_CORS_HEADERS = {
  "Content-Type": "application/json",
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With, X-Frame-Options",
  "Access-Control-Allow-Credentials": "false",
  "X-Frame-Options": "ALLOWALL",
  "Content-Security-Policy":
    "frame-ancestors *; script-src 'self' 'unsafe-inline' https://test-api.payrix.com https://api.payrix.com; connect-src 'self' https://test-api.payrix.com https://api.payrix.com;",
  "X-Content-Type-Options": "nosniff",
  "X-XSS-Protection": "1; mode=block",
  "Referrer-Policy": "strict-origin-when-cross-origin",
  "Permissions-Policy": "payment=*, geolocation=(), microphone=(), camera=()",
} as const;

export const createResponse = (statusCode: number, data: object): APIGatewayProxyResult => ({
  statusCode,
  headers: CORS_HEADERS,
  body: JSON.stringify(data),
});

export const createIframeResponse = (statusCode: number, data: object): APIGatewayProxyResult => ({
  statusCode,
  headers: IFRAME_CORS_HEADERS,
  body: JSON.stringify(data),
});

export const createSuccessResponse = (data: object) => createResponse(200, data);

export const createErrorResponse = (statusCode: number, error: string, message?: string) =>
  createResponse(statusCode, { error, ...(message && { message }) });
