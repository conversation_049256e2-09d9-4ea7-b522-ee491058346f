import moment from "moment-timezone";

export enum LogLevel {
  ERROR = "ERROR",
  WARN = "WARN",
  INFO = "INFO",
  DEBUG = "DEBUG",
}

interface LogContext {
  [key: string]: unknown;
}

const colors = {
  reset: "\x1b[0m",
  red: "\x1b[31m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  gray: "\x1b[90m",
  green: "\x1b[32m",
} as const;

const levelColors = {
  [LogLevel.ERROR]: colors.red,
  [LogLevel.WARN]: colors.yellow,
  [LogLevel.INFO]: colors.blue,
  [LogLevel.DEBUG]: colors.gray,
} as const;

class Logger {
  private context: LogContext = {};

  setContext(context: LogContext): void {
    this.context = { ...this.context, ...context };
  }

  clearContext(): void {
    this.context = {};
  }

  private log(level: LogLevel, message: string, data?: LogContext): void {
    const timestamp = moment().tz("America/Chicago").format();
    const logEntry = {
      timestamp,
      level,
      message,
      ...this.context,
      ...data,
    };

    const color = levelColors[level];
    const coloredOutput = `${color}${JSON.stringify(logEntry)}${colors.reset}`;

    console.log(coloredOutput);
  }

  error(message: string, data?: LogContext): void {
    this.log(LogLevel.ERROR, message, data);
  }

  warn(message: string, data?: LogContext): void {
    this.log(LogLevel.WARN, message, data);
  }

  info(message: string, data?: LogContext): void {
    this.log(LogLevel.INFO, message, data);
  }

  debug(message: string, data?: LogContext): void {
    this.log(LogLevel.DEBUG, message, data);
  }
}

export const logger = new Logger();
