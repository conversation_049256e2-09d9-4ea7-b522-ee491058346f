export interface PayrixMerchantResponse {
  id: string;
  [key: string]: unknown;
}

export interface PayrixUserResponse {
  id: string;
  email: string;
  sanitizedUsername?: string;
  originalUsername?: string;
  [key: string]: unknown;
}

export interface PayrixMerchantEntity {
  id: string;
  email: string;
  legal_name: string;
  dba: string;
  status: number;
  inactive: number;
  frozen: number;
  created: string;
  modified: string;
  [key: string]: unknown;
}

export interface PayrixError {
  field: string;
  code: number;
  severity: number;
  msg: string;
  errorCode: string;
}

export interface PaymentCustomerInfo {
  email?: string;
  name?: string;
  address?: {
    line1?: string;
    line2?: string;
    city?: string;
    state?: string;
    zip?: string;
    country?: string;
  };
}

export interface TokenPaymentData {
  merchantId: string;
  token: string;
  amount: number;
  description?: string;
  customerInfo?: PaymentCustomerInfo;
}

export interface UserAccountData {
  username: string;
  password: string;
  first: string;
  last: string;
  email: string;
  merchantId?: string;
}

export interface PaymentResult {
  success: boolean;
  transaction?: Record<string, unknown>;
  error?: string;
}

export interface MerchantValidationResult {
  isValid: boolean;
  merchant?: Record<string, unknown>;
  error?: string;
}

export interface TokenDeletionResult {
  success: boolean;
  error?: string;
}