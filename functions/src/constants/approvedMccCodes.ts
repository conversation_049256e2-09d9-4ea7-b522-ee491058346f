export interface MCCCode {
  code: string;
  description: string;
  category: string;
}

export const APPROVED_MCC_CODES: MCCCode[] = [
  { code: "5812", description: "Eating Places and Restaurants", category: "Food & Beverage" },
  { code: "5813", description: "Drinking Places (Alcoholic Beverages) – Bars, Taverns, Nightclubs", category: "Food & Beverage" },
  { code: "5814", description: "Fast Food Restaurants", category: "Food & Beverage" },

  { code: "5611", description: "Men's and Boys' Clothing and Accessories Stores", category: "Retail & Apparel" },
  { code: "5621", description: "Women's Ready-to-Wear Stores", category: "Retail & Apparel" },
  { code: "5631", description: "Women's Accessory and Specialty Shops", category: "Retail & Apparel" },
  { code: "5641", description: "Children's and Infants' Wear Stores", category: "Retail & Apparel" },
  { code: "5651", description: "Family Clothing Stores", category: "Retail & Apparel" },
  { code: "5661", description: "Shoe Stores", category: "Retail & Apparel" },
  { code: "5691", description: "Men's and Women's Clothing Stores", category: "Retail & Apparel" },
  { code: "5699", description: "Miscellaneous Apparel and Accessory Shops", category: "Retail & Apparel" },
  { code: "5977", description: "Cosmetic Stores", category: "Retail & Apparel" },
  { code: "7230", description: "Beauty and Barber Shops", category: "Retail & Apparel" },

  { code: "7394", description: "Equipment, Tool, Furniture, and Appliance Rental and Leasing", category: "Equipment & Vehicle Rentals" },
  { code: "7513", description: "Truck and Utility Trailer Rentals", category: "Equipment & Vehicle Rentals" },
  { code: "7519", description: "Motor Home and Recreational Vehicle Rentals", category: "Equipment & Vehicle Rentals" },

  { code: "6513", description: "Real Estate Agents and Managers", category: "Property Management" },
];

export const isApprovedMCCCode = (mccCode: string): boolean => {
  return APPROVED_MCC_CODES.some((mcc) => mcc.code === mccCode);
};

export const getApprovedMCCCodes = (): string[] => {
  return APPROVED_MCC_CODES.map((mcc) => mcc.code);
};

export const getMCCCodeDetails = (mccCode: string): MCCCode | undefined => {
  return APPROVED_MCC_CODES.find((mcc) => mcc.code === mccCode);
};
