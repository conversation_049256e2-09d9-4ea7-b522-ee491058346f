import { randomBytes } from "crypto";
import { setToken, validateToken as validateTokenData, markTokenAsUsed as markTokenUsedInStorage } from "../../../service/token-storage.js";
import { TokenValidationResult } from "../../../types/integration-token.types.js";

export function generateSecureToken(): string {
  return randomBytes(32).toString("hex");
}

export async function validateToken(token: string): Promise<TokenValidationResult> {
  return await validateTokenData(token);
}

export async function markTokenAsUsed(token: string): Promise<boolean> {
  return await markTokenUsedInStorage(token);
}

export async function storeToken(
  token: string,
  data: {
    merchantId: string;
    description: string;
    amount: number;
    returnUrl?: string;
    expiresAt: Date;
    used: boolean;
    currency?: string;
    items?: Array<{
      name: string;
      description?: string;
      quantity: number;
      unitPrice: number;
      total: number;
      commodityCode?: string;
      productCode?: string;
    }>;
    taxAmount?: number;
    shippingAmount?: number;
    dutyAmount?: number;
    orderNumber?: string;
    invoiceNumber?: string;
    customerCode?: string;
    orderDiscount?: number;
    merchantInfo?: {
      address?: {
        line1?: string;
        line2?: string;
        city?: string;
        state?: string;
        zip?: string;
        country?: string;
      };
      contactEmail?: string;
      contactPhone?: string;
    };
  }
): Promise<void> {
  await setToken(token, data);
}