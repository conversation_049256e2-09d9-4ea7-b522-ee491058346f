import { APIGatewayProxyHandler } from "aws-lambda";
import { PayrixService } from "../../service/payrix.service.js";
import { validateOnboardingRequest } from "./schemas/onboarding.schema.js";
import { createErrorResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";
import { checkExistingMerchant } from "./utils/merchant-validation.js";
import { handlePayrixError, handleValidationError } from "./utils/error-handling.js";
import { createUserAccountIfRequested } from "./services/user-account.service.js";
import { buildSuccessResponse } from "./utils/response-builders.js";

export const handler: APIGatewayProxyHandler = async (event) => {
  const requestId = event.requestContext.requestId;
  const clientIp = event.requestContext.identity?.sourceIp || event.headers["X-Forwarded-For"] || event.headers["x-forwarded-for"] || "unknown";

  try {
    logger.info("Merchant onboarding started - Direct Payrix Integration", { requestId, clientIp });

    const body = JSON.parse(event.body || "{}");
    const validation = validateOnboardingRequest(body);

    if (!validation.success || !validation.data) {
      return handleValidationError(validation.errors || ["Unknown validation error"], requestId, clientIp);
    }

    const data = validation.data;

    logger.info("Processing direct Payrix submission", {
      requestId,
      email: data.email,
      legalName: data.name,
    });

    const merchantCheck = await checkExistingMerchant(data.email, data.ein, requestId);
    if (merchantCheck.exists && merchantCheck.response) {
      return merchantCheck.response;
    }

    const payrixService = new PayrixService();
    let payrixResponse: { id: string; [key: string]: unknown };
    let payrixEntityId: string;

    try {
      logger.info("Creating merchant in Payrix (direct integration)", { requestId });

      payrixResponse = await payrixService.createMerchant(data);
      payrixEntityId = payrixResponse?.id;

      if (!payrixEntityId) {
        throw new Error("Payrix response did not contain entity ID");
      }

      logger.info("Payrix merchant created successfully (direct integration)", {
        requestId,
        payrixEntityId,
      });

      const userAccountResult = await createUserAccountIfRequested(data, payrixEntityId, requestId);

      return buildSuccessResponse(
        data,
        payrixResponse,
        payrixEntityId,
        userAccountResult.success ? userAccountResult.data || null : null
      );
    } catch (payrixError) {
      return handlePayrixError(payrixError as Error, requestId);
    }
  } catch (error) {
    const err = error as Error;
    logger.error("Error in onboard handler", {
      requestId,
      error: err.message,
      stack: err.stack,
    });

    return createErrorResponse(500, "Internal Server Error", err.message);
  }
};