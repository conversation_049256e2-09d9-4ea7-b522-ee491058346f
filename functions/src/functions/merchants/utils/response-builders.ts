import { createResponse } from "../../../helpers/response.js";
import type { APIGatewayProxyResult } from "aws-lambda";
import type { OnboardingRequest } from "../schemas/onboarding.schema.js";

interface PayrixResponse {
  id: string;
  [key: string]: unknown;
}

interface UserAccountData {
  id: string;
  email: string;
  sanitizedUsername?: string;
  originalUsername?: string;
  [key: string]: unknown;
}

export function buildSuccessResponse(
  data: OnboardingRequest,
  payrixResponse: PayrixResponse,
  payrixEntityId: string,
  userAccountData: UserAccountData | null
): APIGatewayProxyResult {
  return createResponse(201, {
    success: true,
    payrixEntityId,
    message: "Merchant onboarding completed successfully via direct Payrix integration",
    data: {
      merchant: {
        legal_name: data.name,
        email: data.email,
        verification_status: 1,
      },
      payrixResponse,
      userAccount: userAccountData
        ? {
            id: userAccountData.id,
            username: userAccountData.sanitizedUsername || data.username,
            originalUsername: data.username,
            email: userAccountData.email,
            created: true,
          }
        : null,
    },
  });
}