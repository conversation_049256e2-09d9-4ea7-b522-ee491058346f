import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { logger } from "../helpers/logger.js";

export interface ValidationResult {
  isValid: boolean;
  statusCode?: number;
  error?: string;
  message?: string;
  details?: Record<string, unknown>;
}

// Input sanitization function
export function sanitizeInput(input: string): string {
  if (!input || typeof input !== 'string') {
    return '';
  }
  
  // Remove potentially dangerous characters and normalize whitespace
  return input
    .replace(/[<>"'&]/g, '') // Remove HTML/XML special characters
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim()
    .substring(0, 500); // Limit length
}

// Token format validation
export function validateTokenFormat(token: string): ValidationResult {
  if (!token || typeof token !== 'string') {
    return {
      isValid: false,
      statusCode: 400,
      error: "Invalid token format",
      message: "Token must be a non-empty string"
    };
  }

  // Check if token is hex string of expected length (64 characters for 32 bytes)
  const hexPattern = /^[a-f0-9]{64}$/i;
  if (!hexPattern.test(token)) {
    return {
      isValid: false,
      statusCode: 400,
      error: "Invalid token format",
      message: "Token must be a 64-character hexadecimal string"
    };
  }

  return { isValid: true };
}

// Merchant ID validation
export function validateMerchantIdFormat(merchantId: string): ValidationResult {
  if (!merchantId || typeof merchantId !== 'string') {
    return {
      isValid: false,
      statusCode: 400,
      error: "Invalid merchant ID",
      message: "Merchant ID is required and must be a string"
    };
  }

  // Merchant ID should be alphanumeric and reasonable length
  const merchantIdPattern = /^[a-zA-Z0-9_-]{1,50}$/;
  if (!merchantIdPattern.test(merchantId)) {
    return {
      isValid: false,
      statusCode: 400,
      error: "Invalid merchant ID format",
      message: "Merchant ID must be alphanumeric with optional hyphens/underscores, 1-50 characters"
    };
  }

  return { isValid: true };
}

// Description validation
export function validateDescription(description: string): ValidationResult {
  if (!description || typeof description !== 'string') {
    return {
      isValid: false,
      statusCode: 400,
      error: "Invalid description",
      message: "Description is required and must be a string"
    };
  }

  const trimmedDescription = description.trim();
  if (trimmedDescription.length < 3) {
    return {
      isValid: false,
      statusCode: 400,
      error: "Description too short",
      message: "Description must be at least 3 characters long"
    };
  }

  if (trimmedDescription.length > 255) {
    return {
      isValid: false,
      statusCode: 400,
      error: "Description too long",
      message: "Description must be no more than 255 characters long"
    };
  }

  return { isValid: true };
}

// Amount validation
export function validateAmount(amount: number): ValidationResult {
  if (typeof amount !== 'number' || isNaN(amount)) {
    return {
      isValid: false,
      statusCode: 400,
      error: "Invalid amount",
      message: "Amount must be a valid number"
    };
  }

  if (amount <= 0) {
    return {
      isValid: false,
      statusCode: 400,
      error: "Invalid amount",
      message: "Amount must be greater than 0"
    };
  }

  // Maximum amount check (e.g., $100,000 = 10,000,000 cents)
  if (amount > 10000000) {
    return {
      isValid: false,
      statusCode: 400,
      error: "Amount too large",
      message: "Amount cannot exceed $100,000"
    };
  }

  // Minimum amount check (e.g., $0.50 = 50 cents)
  if (amount < 50) {
    return {
      isValid: false,
      statusCode: 400,
      error: "Amount too small",
      message: "Amount must be at least $0.50"
    };
  }

  return { isValid: true };
}

// Return URL validation
export function validateReturnUrl(returnUrl: string): ValidationResult {
  if (!returnUrl || typeof returnUrl !== 'string') {
    return {
      isValid: false,
      statusCode: 400,
      error: "Invalid return URL",
      message: "Return URL is required and must be a string"
    };
  }

  try {
    const url = new URL(returnUrl);
    
    // Only allow HTTPS URLs (except for localhost in development)
    if (url.protocol !== 'https:' && !url.hostname.includes('localhost')) {
      return {
        isValid: false,
        statusCode: 400,
        error: "Invalid return URL protocol",
        message: "Return URL must use HTTPS protocol"
      };
    }

    // Check for reasonable URL length
    if (returnUrl.length > 2048) {
      return {
        isValid: false,
        statusCode: 400,
        error: "Return URL too long",
        message: "Return URL must be no more than 2048 characters"
      };
    }

    return { isValid: true };
  } catch {
    return {
      isValid: false,
      statusCode: 400,
      error: "Invalid return URL format",
      message: "Return URL must be a valid URL"
    };
  }
}

// Security middleware wrapper for iframe endpoints
export function withIframeSecurity(
  handler: (event: APIGatewayProxyEvent) => Promise<APIGatewayProxyResult>
) {
  return async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
      // Log security-relevant information
      logger.info("Security middleware: Processing request", {
        method: event.httpMethod,
        path: event.path,
        origin: event.headers?.origin || event.headers?.Origin,
        userAgent: event.headers?.['user-agent'] || event.headers?.['User-Agent'],
        ip: event.requestContext?.identity?.sourceIp
      });

      // Basic security headers validation
      const origin = event.headers?.origin || event.headers?.Origin;
      if (origin) {
        // Log origin for monitoring
        logger.info("Request origin", { origin });
      }

      // Execute the wrapped handler
      const result = await handler(event);

      // Add security headers to response
      const securityHeaders = {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'SAMEORIGIN',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' https://api.payrix.com https://test-api.payrix.com; connect-src 'self' https://api.payrix.com https://test-api.payrix.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; frame-ancestors *;",
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,OPTIONS'
      };

      return {
        ...result,
        headers: {
          ...result.headers,
          ...securityHeaders
        }
      };

    } catch (error) {
      logger.error("Security middleware error", { error });
      
      return {
        statusCode: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          error: "Internal server error",
          message: "Security middleware failed"
        })
      };
    }
  };
}
