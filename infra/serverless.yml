service: auth-clear-infra

plugins:
  - serverless-iam-roles-per-function
  - serverless-export-env
  - serverless-offline

provider:
  name: aws
  runtime: nodejs20.x
  stage: ${opt:stage, 'dev'}
  region: us-east-1
  timeout: 30
  environment:
    STAGE: ${self:provider.stage}

resources:
  Resources:
    MainVPC:
      Type: AWS::EC2::VPC
      Properties:
        CidrBlock: 10.0.0.0/16
        EnableDnsSupport: true
        EnableDnsHostnames: true
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-vpc

    InternetGateway:
      Type: AWS::EC2::InternetGateway
      Properties:
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-igw

    InternetGatewayAttachment:
      Type: AWS::EC2::VPCGatewayAttachment
      Properties:
        InternetGatewayId: !Ref InternetGateway
        VpcId: !Ref MainVPC

    PublicSubnet:
      Type: AWS::EC2::Subnet
      Properties:
        VpcId: !Ref MainVPC
        AvailabilityZone: ${self:provider.region}a
        CidrBlock: ********/24
        MapPublicIpOnLaunch: true
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-public-subnet

    PrivateSubnetA:
      Type: AWS::EC2::Subnet
      Properties:
        VpcId: !Ref MainVPC
        AvailabilityZone: ${self:provider.region}a
        CidrBlock: ********/24
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-lambda-subnet-a

    PrivateSubnetB:
      Type: AWS::EC2::Subnet
      Properties:
        VpcId: !Ref MainVPC
        AvailabilityZone: ${self:provider.region}b
        CidrBlock: ********/24
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-lambda-subnet-b

    PublicRouteTable:
      Type: AWS::EC2::RouteTable
      Properties:
        VpcId: !Ref MainVPC
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-public-rt

    PublicRoute:
      Type: AWS::EC2::Route
      DependsOn: InternetGatewayAttachment
      Properties:
        RouteTableId: !Ref PublicRouteTable
        DestinationCidrBlock: 0.0.0.0/0
        GatewayId: !Ref InternetGateway

    PublicSubnetRouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        RouteTableId: !Ref PublicRouteTable
        SubnetId: !Ref PublicSubnet

    NatGatewayEIP:
      Type: AWS::EC2::EIP
      DependsOn: InternetGatewayAttachment
      Properties:
        Domain: vpc

    NatGateway:
      Type: AWS::EC2::NatGateway
      Properties:
        AllocationId: !GetAtt NatGatewayEIP.AllocationId
        SubnetId: !Ref PublicSubnet
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-nat

    LambdaRouteTable:
      Type: AWS::EC2::RouteTable
      Properties:
        VpcId: !Ref MainVPC
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-lambda-rt

    LambdaRoute:
      Type: AWS::EC2::Route
      Properties:
        RouteTableId: !Ref LambdaRouteTable
        DestinationCidrBlock: 0.0.0.0/0
        NatGatewayId: !Ref NatGateway

    PrivateSubnetARouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        RouteTableId: !Ref LambdaRouteTable
        SubnetId: !Ref PrivateSubnetA

    PrivateSubnetBRouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        RouteTableId: !Ref LambdaRouteTable
        SubnetId: !Ref PrivateSubnetB

    LambdaSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupDescription: Security group for Lambda functions
        VpcId: !Ref MainVPC
        SecurityGroupEgress:
          - IpProtocol: -1
            CidrIp: 0.0.0.0/0
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}-lambda-sg

  Outputs:
    VpcId:
      Value: !Ref MainVPC
      Export:
        Name: ${self:service}-${self:provider.stage}-vpc-id

    PrivateSubnetA:
      Value: !Ref PrivateSubnetA
      Export:
        Name: ${self:service}-${self:provider.stage}-private-subnet-a

    PrivateSubnetB:
      Value: !Ref PrivateSubnetB
      Export:
        Name: ${self:service}-${self:provider.stage}-private-subnet-b

    LambdaSecurityGroupId:
      Value: !Ref LambdaSecurityGroup
      Export:
        Name: ${self:service}-${self:provider.stage}-lambda-sg-id

    PublicSubnet:
      Value: !Ref PublicSubnet
      Export:
        Name: ${self:service}-${self:provider.stage}-public-subnet

package:
  individually: true
  exclude:
    - node_modules/**
  include:
    - src/**