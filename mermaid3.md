sequenceDiagram
    participant Customer
    participant Frontend
    participant APIGateway
    participant TokenGenLambda
    participant DynamoDB
    participant PayrixAPI
    participant PaymentIframe
    participant PayFieldsSDK
    participant ProcessPaymentLambda

    Note over Customer, ProcessPaymentLambda: Payment Token Lifecycle & Data Flow

    %% Token Generation Phase
    Customer->>Frontend: Initiate Payment
    Frontend->>APIGateway: POST /payments/generate-integration-token
    APIGateway->>TokenGenLambda: Invoke with merchant data
    
    TokenGenLambda->>PayrixAPI: Validate Merchant ID
    PayrixAPI-->>TokenGenLambda: Merchant validation result
    
    alt Merchant Valid
        TokenGenLambda->>TokenGenLambda: Generate secure token (32 bytes hex)
        TokenGenLambda->>DynamoDB: Store token with TTL
        Note over DynamoDB: Token stored with expiration (1-1440 min)
        TokenGenLambda-->>APIGateway: Return embed URL + token
        APIGateway-->>Frontend: Integration token response
        
        Frontend->>PaymentIframe: Load iframe with embed URL
        PaymentIframe->>PayFieldsSDK: Initialize PayFields
        Note over PayFieldsSDK: mode: 'token', txnType: 'auth', amount: '0'
        
    else Merchant Invalid
        TokenGenLambda-->>APIGateway: Return validation error
        APIGateway-->>Frontend: Error response
        Frontend->>Customer: Show error message
    end

    %% Payment Processing Phase
    Customer->>PaymentIframe: Fill payment form
    Customer->>PaymentIframe: Enter billing address
    Customer->>PaymentIframe: Accept terms & submit
    
    PaymentIframe->>PayFieldsSDK: Submit payment data
    PayFieldsSDK->>PayrixAPI: Create payment token
    PayrixAPI-->>PayFieldsSDK: Return payment token
    
    alt Token Creation Success
        PayFieldsSDK-->>PaymentIframe: Payment token created
        PaymentIframe->>Frontend: Post message with token
        Frontend->>APIGateway: POST /payments/process-token-payment
        APIGateway->>ProcessPaymentLambda: Process payment request
        
        ProcessPaymentLambda->>PayrixAPI: Validate merchant again
        PayrixAPI-->>ProcessPaymentLambda: Merchant validation
        
        ProcessPaymentLambda->>PayrixAPI: POST /txns (process payment)
        PayrixAPI-->>ProcessPaymentLambda: Transaction result
        
        alt Payment Success
            ProcessPaymentLambda->>PayrixAPI: DELETE /tokens/{id} (cleanup)
            PayrixAPI-->>ProcessPaymentLambda: Token deleted
            ProcessPaymentLambda-->>APIGateway: Success response
            APIGateway-->>Frontend: Payment successful
            Frontend->>Customer: Show success message
            
        else Payment Failed
            ProcessPaymentLambda->>PayrixAPI: DELETE /tokens/{id} (cleanup)
            ProcessPaymentLambda-->>APIGateway: Payment error
            APIGateway-->>Frontend: Payment failed
            Frontend->>Customer: Show payment error
        end
        
    else Token Creation Failed
        PayFieldsSDK-->>PaymentIframe: Token creation error
        PaymentIframe->>Frontend: Post error message
        Frontend->>Customer: Show tokenization error
    end

    %% Token Expiry & Cleanup
    Note over DynamoDB: Automatic TTL cleanup
    DynamoDB->>DynamoDB: Delete expired tokens automatically
    
    %% Error Scenarios
    Note over Customer, ProcessPaymentLambda: Error Handling Scenarios
    
    alt Integration Token Expired
        Frontend->>APIGateway: Validate token status
        APIGateway->>DynamoDB: Check token validity
        DynamoDB-->>APIGateway: Token expired/used/invalid
        APIGateway-->>Frontend: Token status error
        Frontend->>Customer: Request new token generation
    end
    
    alt Network/API Errors
        Frontend->>APIGateway: API Request
        APIGateway-->>Frontend: Network/500 error
        Frontend->>Frontend: Retry mechanism (if applicable)
        Frontend->>Customer: Show error with retry option
    end