#!/bin/bash

# Script to update CloudFront distribution ID in package.json
# Usage: ./update-cloudfront-id.sh [stage] [profile]

STAGE=${1:-dev}
PROFILE=${2:-payrix}

echo "Getting CloudFront distribution ID for stage: $STAGE"

# Get CloudFront distribution info
DISTRIBUTION_INFO=$(aws cloudfront list-distributions --profile $PROFILE --output json | jq -r '.DistributionList.Items[0] | {Id: .Id, DomainName: .DomainName}')
DISTRIBUTION_ID=$(echo $DISTRIBUTION_INFO | jq -r '.Id')
DISTRIBUTION_DOMAIN=$(echo $DISTRIBUTION_INFO | jq -r '.DomainName')

if [ "$DISTRIBUTION_ID" == "null" ] || [ -z "$DISTRIBUTION_ID" ]; then
    echo "Error: Could not find CloudFront distribution"
    exit 1
fi

echo "Found CloudFront distribution:"
echo "  ID: $DISTRIBUTION_ID"
echo "  Domain: $DISTRIBUTION_DOMAIN"

# Update package.json with the correct distribution ID
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    sed -i '' "s/--distribution-id [A-Z0-9]*/--distribution-id $DISTRIBUTION_ID/g" package.json
else
    # Linux
    sed -i "s/--distribution-id [A-Z0-9]*/--distribution-id $DISTRIBUTION_ID/g" package.json
fi

echo "Updated package.json with distribution ID: $DISTRIBUTION_ID"

# Output both values for easy copying
echo ""
echo "=== CloudFront Information ==="
echo "Distribution ID: $DISTRIBUTION_ID"
echo "Domain: $DISTRIBUTION_DOMAIN"
echo "Full URL: https://$DISTRIBUTION_DOMAIN" 