service: auth-clear-frontend

provider:
  name: aws
  runtime: nodejs20.x
  stage: ${opt:stage, 'dev'}
  region: us-east-1

resources:
  Resources:
    FrontendBucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: ${self:service}-${self:provider.stage}
        AccessControl: Private
        PublicAccessBlockConfiguration:
          BlockPublicAcls: true
          BlockPublicPolicy: true
          IgnorePublicAcls: true
          RestrictPublicBuckets: true
        Tags:
          - Key: Name
            Value: ${self:service}-${self:provider.stage}
          - Key: Environment
            Value: ${self:provider.stage}

    CloudFrontOriginAccessControl:
      Type: AWS::CloudFront::OriginAccessControl
      Properties:
        OriginAccessControlConfig:
          Name: ${self:service}-${self:provider.stage}-s3-oac
          OriginAccessControlOriginType: s3
          SigningBehavior: always
          SigningProtocol: sigv4

    CloudFrontDistribution:
      Type: AWS::CloudFront::Distribution
      Properties:
        DistributionConfig:
          Enabled: true
          DefaultRootObject: index.html
          HttpVersion: http2
          PriceClass: PriceClass_100
          CustomErrorResponses:
            - ErrorCode: 403
              ResponseCode: 200
              ResponsePagePath: /index.html
            - ErrorCode: 404
              ResponseCode: 200
              ResponsePagePath: /index.html
          Origins:
            - Id: S3Origin
              DomainName: ${self:service}-${self:provider.stage}.s3.amazonaws.com
              S3OriginConfig:
                OriginAccessIdentity: ''
              OriginAccessControlId: !GetAtt CloudFrontOriginAccessControl.Id
          DefaultCacheBehavior:
            TargetOriginId: S3Origin
            ViewerProtocolPolicy: redirect-to-https
            CachePolicyId: 658327ea-f89d-4fab-a63d-7e88639e58f6
            OriginRequestPolicyId: 88a5eaf4-2fd4-4709-b370-b4c650ea3fcf
            Compress: true
            FunctionAssociations: []

    FrontendBucketPolicy:
      Type: AWS::S3::BucketPolicy
      Properties:
        Bucket: !Ref FrontendBucket
        PolicyDocument:
          Statement:
            - Effect: Allow
              Principal:
                Service: cloudfront.amazonaws.com
              Action: s3:GetObject
              Resource: !Join ['', ['arn:aws:s3:::', !Ref FrontendBucket, '/*']]
              Condition:
                StringEquals:
                  AWS:SourceArn: !Join ['', ['arn:aws:cloudfront::', !Ref AWS::AccountId, ':distribution/', !Ref CloudFrontDistribution]]

  Outputs:
    FrontendBucketName:
      Value: !Ref FrontendBucket
      Export:
        Name: ${self:service}-${self:provider.stage}-bucket-name

    CloudFrontDistributionId:
      Value: !Ref CloudFrontDistribution
      Export:
        Name: ${self:service}-${self:provider.stage}-distribution-id

    CloudFrontDomainName:
      Value: !GetAtt CloudFrontDistribution.DomainName
      Export:
        Name: ${self:service}-${self:provider.stage}-domain-name