import type { AxiosResponse } from "axios";
import { apiClient } from "../config";
import type { CreatePayrixMerchantRequest, MerchantResponse } from "../types/merchant";

export const createMerchant = async (merchantData: CreatePayrixMerchantRequest): Promise<MerchantResponse> => {
  try {
    const response: AxiosResponse<MerchantResponse> = await apiClient.post("/merchants/onboard", merchantData);
    return response.data;
  } catch (error) {
    console.error("Error creating merchant:", error);
    throw error;
  }
};