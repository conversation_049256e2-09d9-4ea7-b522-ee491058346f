import type { AxiosResponse, AxiosError } from "axios";
import { apiClient } from "../config";
import type {
  PaymentConfigRequest,
  PaymentConfigResponse,
  GenerateIntegrationTokenRequest,
  GenerateIntegrationTokenResponse,
  ValidateIframeTokenResponse,
  IframeConfigParams,
  IframeConfigResponse,
  TokenStatusResponse,
  TokenPaymentRequest,
  TokenPaymentResponse,
} from "../types/payment";

export const generatePaymentConfig = async (configData: PaymentConfigRequest): Promise<PaymentConfigResponse> => {
  try {
    const response: AxiosResponse<PaymentConfigResponse> = await apiClient.post("/payments/generate-payment-config", configData);
    return response.data;
  } catch (error) {
    console.error("Error generating payment config:", error);
    throw error;
  }
};

export const generateIntegrationToken = async (tokenData: GenerateIntegrationTokenRequest): Promise<GenerateIntegrationTokenResponse> => {
  try {
    const response: AxiosResponse<GenerateIntegrationTokenResponse> = await apiClient.post("/payments/generate-integration-token", tokenData);
    return response.data;
  } catch (error) {
    console.error("Error generating integration token:", error);
    throw error;
  }
};

export const validateIframeToken = async (token: string): Promise<ValidateIframeTokenResponse> => {
  try {
    const response: AxiosResponse<ValidateIframeTokenResponse> = await apiClient.post("/payments/validate-iframe-token", { token });
    return response.data;
  } catch (error) {
    console.error("Error validating iframe token:", error);
    throw error;
  }
};

export const getIframeConfig = async (params?: IframeConfigParams): Promise<IframeConfigResponse> => {
  try {
    const response: AxiosResponse<IframeConfigResponse> = await apiClient.get("/payments/iframe-config", { params });
    return response.data;
  } catch (error) {
    console.error("Error getting iframe configuration:", error);
    throw error;
  }
};

export const checkTokenStatus = async (token: string): Promise<TokenStatusResponse> => {
  try {
    const response: AxiosResponse<TokenStatusResponse> = await apiClient.get("/payments/token-status", {
      params: { token },
    });
    return response.data;
  } catch (error) {
    console.error("Error checking token status:", error);
    throw error;
  }
};

export const processTokenPayment = async (paymentData: TokenPaymentRequest): Promise<TokenPaymentResponse> => {
  try {
    console.log("Processing token payment:", {
      merchantId: paymentData.merchantId,
      token: paymentData.token.substring(0, 8) + "...",
      amount: paymentData.amount,
      description: paymentData.description,
    });

    const response: AxiosResponse<TokenPaymentResponse> = await apiClient.post("/payments/process-token-payment", paymentData);

    console.log("Token payment processed successfully:", {
      transactionId: response.data.transaction?.id,
      status: response.data.transaction?.status,
      amount: response.data.transaction?.amount,
    });

    return response.data;
  } catch (error) {
    console.error("Error processing token payment:", error);
    const axiosError = error as AxiosError;
    if (axiosError.response?.data) {
      return axiosError.response.data as TokenPaymentResponse;
    }
    return {
      success: false,
      message: "Network error occurred during token payment processing",
      error: axiosError.message,
    };
  }
};