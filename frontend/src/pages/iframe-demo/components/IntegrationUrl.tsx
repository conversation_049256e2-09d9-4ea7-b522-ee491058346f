import { toast } from "sonner";

interface IntegrationUrlProps {
  embedUrl: string;
}

export const IntegrationUrl = ({ embedUrl }: IntegrationUrlProps) => {
  const handleCopyUrl = () => {
    navigator.clipboard.writeText(embedUrl);
    toast.success("URL copied to clipboard!");
  };

  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <p className="text-slate-600">Use this URL to embed the payment iframe in your application:</p>
        <button
          onClick={handleCopyUrl}
          className="px-4 py-2 bg-slate-800 text-white rounded-lg hover:bg-slate-900 transition-colors text-sm font-medium shadow-sm hover:shadow-md"
        >
          Copy URL
        </button>
      </div>
      <code className="text-sm text-slate-700 break-all bg-white p-4 rounded-lg border block font-mono shadow-inner">
        {embedUrl}
      </code>
    </>
  );
};