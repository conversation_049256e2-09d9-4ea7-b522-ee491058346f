import { RefObject } from "react";

interface PaymentIframePreviewProps {
  embedUrl: string;
  iframeRef: RefObject<HTMLIFrameElement | null>;
  iframeLoaded: boolean;
}

export const PaymentIframePreview = ({ embedUrl, iframeRef, iframeLoaded }: PaymentIframePreviewProps) => {
  if (!embedUrl) {
    return (
      <div className="bg-gradient-to-br from-slate-50 to-gray-50 border-2 border-dashed border-slate-300 rounded-xl p-12 text-center">
        <div className="text-slate-500">
          <svg className="w-16 h-16 mx-auto mb-6 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <p className="text-xl font-semibold text-slate-700 mb-2">Generate a token to load the payment iframe</p>
          <p className="text-slate-600">Configure your payment details and click &quot;Generate Integration Token&quot;</p>
        </div>
      </div>
    );
  }

  return (
    <div className="border border-slate-300 rounded-xl overflow-hidden shadow-lg">
      <div className="bg-gradient-to-r from-slate-50 to-gray-50 px-6 py-3 border-b border-slate-200 flex items-center justify-between">
        <span className="text-sm font-medium text-slate-700">Payment Iframe</span>
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${iframeLoaded ? "bg-slate-600" : "bg-slate-400"} shadow-sm`}></div>
          <span className="text-xs text-slate-600 font-medium">{iframeLoaded ? "Loaded" : "Loading..."}</span>
        </div>
      </div>
      <div className="aspect-video lg:aspect-[16/10]">
        <iframe 
          ref={iframeRef} 
          src={embedUrl} 
          className="w-full h-full border-none" 
          allow="payment" 
          title="Payment Iframe Demo" 
        />
      </div>
    </div>
  );
};