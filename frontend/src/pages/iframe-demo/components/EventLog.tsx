import { PaymentEvent } from "../types";

interface EventLogProps {
  events: PaymentEvent[];
  onClearEvents: () => void;
}

export const EventLog = ({ events, onClearEvents }: EventLogProps) => {
  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <p className="text-slate-600">Real-time events from the payment iframe</p>
        <button
          onClick={onClearEvents}
          className="text-sm text-slate-500 hover:text-slate-700 px-3 py-1 rounded-md hover:bg-slate-100 transition-colors"
        >
          Clear Events
        </button>
      </div>
      <div className="bg-slate-900 text-slate-300 p-4 rounded-lg h-64 overflow-y-auto font-mono text-sm shadow-inner">
        {events.length === 0 ? (
          <div className="text-slate-500 text-center py-8">
            <svg className="w-8 h-8 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            No events yet. Generate a token and interact with the iframe to see events.
          </div>
        ) : (
          events.map((event, index) => (
            <div key={index} className="mb-2 border-l-2 border-slate-500/30 pl-3">
              <span className="text-slate-400">[{new Date(event.timestamp).toLocaleTimeString()}]</span>
              <span className="text-slate-200 ml-2 font-semibold">{event.type}</span>
              {event.data && (
                <div className="text-slate-300 ml-4 text-xs mt-1 bg-slate-800/50 p-2 rounded">
                  {typeof event.data === "string" ? event.data : JSON.stringify(event.data, null, 2)}
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </>
  );
};