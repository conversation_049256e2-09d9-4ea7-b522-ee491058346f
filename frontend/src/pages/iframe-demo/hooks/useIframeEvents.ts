import { useEffect, useState } from "react";
import { toast } from "sonner";
import { PaymentEvent } from "../types";
import { EVENT_TYPES, MAX_EVENTS } from "../constants";

export const useIframeEvents = () => {
  const [events, setEvents] = useState<PaymentEvent[]>([]);
  const [iframeLoaded, setIframeLoaded] = useState(false);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const eventData: PaymentEvent = {
        type: event.data.type,
        data: event.data.data || event.data.error || event.data,
        timestamp: new Date().toISOString(),
      };

      setEvents((prev) => [eventData, ...prev.slice(0, MAX_EVENTS - 1)]);

      switch (event.data.type) {
        case EVENT_TYPES.IFRAME_READY:
          setIframeLoaded(true);
          toast.success("Payment iframe loaded successfully!");
          break;
        case EVENT_TYPES.SUCCESS:
          toast.success("Payment successful!");
          break;
        case EVENT_TYPES.FAILURE:
          toast.error(`Payment failed: ${event.data.error}`);
          break;
        case EVENT_TYPES.VALIDATION_FAILURE:
          toast.error(`Validation failed: ${event.data.error}`);
          break;
        case EVENT_TYPES.TIMEOUT:
          toast.error("Payment timed out");
          break;
      }
    };

    window.addEventListener("message", handleMessage);
    return () => window.removeEventListener("message", handleMessage);
  }, []);

  const clearEvents = () => setEvents([]);

  const resetIframeState = () => {
    setIframeLoaded(false);
    setEvents([]);
  };

  return {
    events,
    iframeLoaded,
    clearEvents,
    resetIframeState,
  };
};