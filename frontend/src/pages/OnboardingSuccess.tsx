import { Link } from "react-router-dom";

const OnboardingSuccess = () => {
  return (
    <div className="min-h-screen flex items-center justify-center px-6">
      <div className="text-center max-w-md mx-auto">
        <img src="/LogoFiles/svg/Black logo - no background.svg" alt="Auth-Clear" className="h-12 w-auto mx-auto mb-6" />
        <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>

        <h1 className="text-3xl font-semibold text-slate-800 mb-4">Onboarding Complete</h1>

        <p className="text-slate-600 mb-8">Your merchant account has been successfully created. You can now start accepting payments.</p>

        <div className="space-y-3">
          <Link to="/payment" className="block w-full px-6 py-3 bg-slate-900 text-white rounded-lg hover:bg-slate-800 transition-colors">
            Start Processing Payments
          </Link>

          <Link to="/" className="block w-full px-6 py-3 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors">
            Back to Dashboard
          </Link>
        </div>
      </div>
    </div>
  );
};

export default OnboardingSuccess;
