import { Link } from "react-router-dom";

const Home = () => {
  return (
    <div className="min-h-screen flex items-center justify-center px-6">
      <div className="text-center max-w-4xl mx-auto">
        <img src="/LogoFiles/svg/Black logo - no background.svg" alt="Auth-Clear" className="h-20 w-auto mx-auto mb-8" />
        <h1 className="text-5xl font-bold text-slate-800 mb-6">Payment Processing Platform</h1>

        <p className="text-xl text-slate-600 mb-12 max-w-3xl mx-auto">Complete your application in minutes and start processing payments quickly.</p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link
            to="/onboarding"
            className="inline-block px-8 py-4 bg-slate-900 text-white rounded-lg hover:bg-slate-800 transition-colors text-lg font-medium shadow-lg transform hover:scale-105"
          >
            Start Merchant Onboarding
          </Link>
          <Link
            to="/iframe-demo"
            className="inline-block px-8 py-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-lg font-medium shadow-lg transform hover:scale-105"
          >
            Try Iframe Demo
          </Link>
        </div>

        <div className="mt-16 grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="w-12 h-12 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-slate-800 mb-2">Business Information</h3>
            <p className="text-slate-600">Provide your business details, tax information, and contact data</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-slate-800 mb-2">Owner Details</h3>
            <p className="text-slate-600">Submit owner information for KYC compliance and verification</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-slate-800 mb-2">Banking Setup</h3>
            <p className="text-slate-600">Connect your bank account for payment processing and settlements</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;