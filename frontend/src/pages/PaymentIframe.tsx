// Hooks
import { usePaymentIframe } from "../hooks/usePaymentIframe";
import { useBillingAddress } from "../hooks/useBillingAddress";
import { usePaymentHandlers } from "../hooks/usePaymentHandlers";

// Components
import { TransactionDetails } from "../components/payments/iframe/TransactionDetails";
import { OrderSummary } from "../components/payments/iframe/OrderSummary";
import { BillingAddressForm } from "../components/payments/iframe/BillingAddressForm";
import { PaymentFormSection } from "../components/payments/iframe/PaymentFormSection";
import { PaymentLayout } from "../components/layouts/PaymentLayout";
import { LoadingState } from "../components/payments/iframe/LoadingState";
import { ErrorState } from "../components/payments/iframe/ErrorState";
import { SuccessState } from "../components/payments/iframe/SuccessState";

const PaymentIframe = () => {
  // Custom hooks for state management
  const { payFieldsConfig, merchantInfo, paymentInfo, error: iframeError, loading } = usePaymentIframe();
  const { billingAddress, termsAccepted, handleAddressChange, setTermsAccepted, isAddressValid } = useBillingAddress();
  const { success, error: paymentError, handlePaymentSuccess, handlePaymentFailure } = usePaymentHandlers(paymentInfo);

  // Combine errors from different sources
  const error = iframeError || paymentError;

  // Handle different states
  if (loading) {
    return <LoadingState />;
  }

  if (error && !payFieldsConfig) {
    return <ErrorState error={error} />;
  }

  if (success) {
    return <SuccessState paymentInfo={paymentInfo} />;
  }

  // Main payment form using new responsive grid layout system
  return (
    <PaymentLayout>
      {/* Transaction Details at top */}
      {paymentInfo && <TransactionDetails paymentInfo={paymentInfo} />}

      {/* Responsive 3-column grid for desktop, 2-column for tablet, single column for mobile */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6">
        {/* Order Summary - Desktop: left, Tablet: top-left, Mobile: first */}
        <div className="order-1">
          <OrderSummary paymentInfo={paymentInfo} merchantInfo={merchantInfo} />
        </div>

        {/* Billing Address - Desktop: middle, Tablet: right spanning 2 rows, Mobile: second */}
        <div className="order-2 md:row-span-2 xl:row-span-1">
          <BillingAddressForm
            billingAddress={billingAddress}
            termsAccepted={termsAccepted}
            handleAddressChange={handleAddressChange}
            setTermsAccepted={setTermsAccepted}
          />
        </div>

        {/* Payment Form - Desktop: right, Tablet: bottom-left, Mobile: third */}
        <div className="order-3">
          <PaymentFormSection
            payFieldsConfig={payFieldsConfig}
            billingAddress={billingAddress}
            error={error}
            isAddressValid={isAddressValid()}
            onSuccess={handlePaymentSuccess}
            onFailure={handlePaymentFailure}
          />
        </div>
      </div>
    </PaymentLayout>
  );
};

export default PaymentIframe;
