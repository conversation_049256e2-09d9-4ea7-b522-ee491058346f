import { useState } from "react";
import { toast } from "sonner";
import { PageLayout } from "../components/layouts/PageLayout";
import { ContentCard } from "../components/ContentCard";

const IframeIntegrationDocs = () => {
  const [copied, setCopied] = useState<string | null>(null);

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    setCopied(label);
    toast.success(`${label} copied to clipboard!`);
    setTimeout(() => setCopied(null), 2000);
  };

  const exampleHtml = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Integration Example</title>
    <style>
        .payment-container {
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #fff;
        }
        .payment-iframe {
            width: 100%;
            height: 600px;
            border: none;
            border-radius: 8px;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #64748b;
        }
        .error {
            text-align: center;
            padding: 40px;
            color: #dc2626;
            background-color: #fee2e2;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <h2>Secure Payment</h2>
        <div id="payment-iframe-container">
            <div class="loading">Loading payment form...</div>
        </div>
    </div>

    <script>
        // Your merchant configuration
        const merchantConfig = {
            merchantId: 'your-merchant-id',
            description: 'Product Purchase',
            amount: 2500, // $25.00 in cents
            returnUrl: 'https://yoursite.com/payment-success'
        };

        // Generate payment token and load iframe
        async function loadPaymentIframe() {
            try {
                // Step 1: Generate integration token
                const response = await fetch('https://your-api-domain.com/payments/generate-integration-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(merchantConfig)
                });

                const result = await response.json();
                
                if (!result.success) {
                    throw new Error(result.message || 'Failed to generate payment token');
                }

                // Step 2: Create and load iframe
                const iframe = document.createElement('iframe');
                iframe.src = result.data.embedUrl;
                iframe.className = 'payment-iframe';
                iframe.allow = 'payment';
                
                // Replace loading message with iframe
                const container = document.getElementById('payment-iframe-container');
                while (container.firstChild) {
                    container.removeChild(container.firstChild);
                }
                container.appendChild(iframe);

                // Step 3: Listen for payment events
                window.addEventListener('message', handlePaymentMessage);

            } catch (error) {
                console.error('Payment integration error:', error);
                const container = document.getElementById('payment-iframe-container');
                while (container.firstChild) {
                    container.removeChild(container.firstChild);
                }
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error';
                errorDiv.textContent = 'Failed to load payment form. Please try again.';
                container.appendChild(errorDiv);
            }
        }

        // Handle messages from payment iframe
        function handlePaymentMessage(event) {
            // Verify origin for security (replace with your actual domain)
            if (event.origin !== 'https://your-payment-domain.com') {
                return;
            }

            switch (event.data.type) {
                case 'PAYMENT_IFRAME_READY':
                    console.log('Payment iframe loaded successfully');
                    break;
                    
                case 'PAYMENT_SUCCESS':
                    console.log('Payment successful:', event.data.data);
                    // Handle successful payment
                    alert('Payment successful! Thank you for your purchase.');
                    break;
                    
                case 'PAYMENT_FAILURE':
                    console.error('Payment failed:', event.data.error);
                    // Handle payment failure
                    alert('Payment failed: ' + event.data.error);
                    break;
                    
                case 'PAYMENT_REDIRECT':
                    // Handle redirect after successful payment
                    window.location.href = event.data.url;
                    break;
                    
                case 'PAYMENT_IFRAME_ERROR':
                    console.error('Iframe error:', event.data.error);
                    // Handle iframe loading errors
                    break;
            }
        }

        // Load payment iframe when page loads
        document.addEventListener('DOMContentLoaded', loadPaymentIframe);
    </script>
</body>
</html>`;

  const jsExample = `// Modern JavaScript/React Integration Example
import { useEffect, useRef, useState } from 'react';

const PaymentIntegration = ({ merchantId, description, amount, returnUrl }) => {
  const iframeRef = useRef(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadPaymentIframe();
    
    // Listen for payment events
    const handleMessage = (event) => {
      // Verify origin for security
      if (event.origin !== 'https://your-payment-domain.com') return;

      switch (event.data.type) {
        case 'PAYMENT_IFRAME_READY':
          setLoading(false);
          break;
        case 'PAYMENT_SUCCESS':
          onPaymentSuccess(event.data.data);
          break;
        case 'PAYMENT_FAILURE':
          onPaymentFailure(event.data.error);
          break;
        case 'PAYMENT_REDIRECT':
          window.location.href = event.data.url;
          break;
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  const loadPaymentIframe = async () => {
    try {
      const response = await fetch('/api/payments/generate-integration-token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ merchantId, description, amount, returnUrl })
      });

      const result = await response.json();
      if (!result.success) throw new Error(result.message);

      if (iframeRef.current) {
        iframeRef.current.src = result.data.embedUrl;
      }
    } catch (err) {
      setError(err.message);
      setLoading(false);
    }
  };

  const onPaymentSuccess = (data) => {
    console.log('Payment successful:', data);
    // Handle success
  };

  const onPaymentFailure = (error) => {
    console.error('Payment failed:', error);
    setError(error);
  };

  if (error) {
    return <div className="error">Payment Error: {error}</div>;
  }

  return (
    <div className="payment-container">
      {loading && <div className="loading">Loading payment form...</div>}
      <iframe
        ref={iframeRef}
        className="payment-iframe"
        style={{ 
          width: '100%', 
          height: '600px', 
          border: 'none',
          display: loading ? 'none' : 'block'
        }}
        allow="payment"
        title="Secure Payment Form"
      />
    </div>
  );
};

export default PaymentIntegration;`;

  const apiDocs = `
# API Documentation

## Generate Integration Token

**Endpoint:** \`POST /payments/generate-integration-token\`

**Description:** Generates a secure token for iframe payment integration.

**Request Body:**
\`\`\`json
{
  "merchantId": "string (required)",
  "description": "string (required)",
  "amount": "number (optional, in cents)",
  "returnUrl": "string (optional)",
  "expiresIn": "number (optional, minutes, default: 60)"
}
\`\`\`

**Response:**
\`\`\`json
{
  "success": true,
  "message": "Integration token generated successfully",
  "data": {
    "token": "secure-token-string",
    "expiresAt": "2024-01-01T12:00:00.000Z",
    "embedUrl": "https://your-domain.com/payment-iframe?token=...",
    "merchantInfo": {
      "id": "merchant-id",
      "name": "Merchant Name",
      "status": 1
    }
  }
}
\`\`\`

## Validate Iframe Token

**Endpoint:** \`POST /payments/validate-iframe-token\`

**Description:** Validates a token and returns payment configuration for iframe.

**Request Body:**
\`\`\`json
{
  "token": "string (required)"
}
\`\`\`

**Response:**
\`\`\`json
{
  "success": true,
  "message": "Token validated successfully",
  "data": {
    "config": {
      "merchantId": "merchant-id",
      "publicKey": "payrix-public-key",
      "amount": 2500,
      "description": "Product Purchase",
      "mode": "txn",
      "txnType": "sale"
    },
    "merchantInfo": {
      "id": "merchant-id",
      "name": "Merchant Name",
      "status": 1
    },
    "paymentInfo": {
      "description": "Product Purchase",
      "amount": 2500,
      "returnUrl": "https://yoursite.com/success"
    }
  }
}
\`\`\`

## Get Iframe Configuration

**Endpoint:** \`GET /payments/iframe-config\`

**Description:** Gets iframe-specific configuration and styling options.

**Query Parameters:**
- \`domain\` (optional): Your domain for CORS configuration
- \`theme\` (optional): 'light', 'dark', or 'auto'
- \`language\` (optional): Language code (default: 'en')
- \`currency\` (optional): Currency code (default: 'USD')

**Response:**
\`\`\`json
{
  "success": true,
  "data": {
    "payrixConfig": {
      "scriptUrl": "https://test-api.payrix.com/payFieldsScript?spa=1&iframe=1",
      "environment": "test",
      "supportedFeatures": ["iframe-embedding", "auto-resize", "responsive-design"]
    },
    "styling": {
      "theme": "light",
      "customCSS": { /* Theme-specific styles */ }
    },
    "security": {
      "allowedOrigins": ["*"],
      "cspDirectives": ["frame-ancestors *", "script-src 'self' 'unsafe-inline'"]
    },
    "features": {
      "autoResize": true,
      "responsiveDesign": true,
      "mobileOptimized": true
    }
  }
}
\`\`\`

## Check Token Status

**Endpoint:** \`GET /payments/token-status?token=TOKEN\`

**Description:** Checks the status and validity of a payment token.

**Response:**
\`\`\`json
{
  "success": true,
  "data": {
    "isValid": true,
    "status": "valid",
    "expiresAt": "2024-01-01T12:00:00.000Z",
    "timeRemaining": 3600,
    "merchantId": "merchant-id",
    "amount": 2500,
    "description": "Product Purchase"
  }
}
\`\`\`

## Iframe Events

The payment iframe communicates with the parent window using \`postMessage\`. Listen for these events:

### PAYMENT_IFRAME_READY
Fired when the iframe has loaded and is ready to accept payments.
\`\`\`javascript
{
  type: 'PAYMENT_IFRAME_READY',
  data: {
    merchantName: 'Merchant Name',
    amount: 2500,
    description: 'Product Purchase'
  }
}
\`\`\`

### PAYFIELDS_SCRIPT_LOADED
Fired when the Payrix PayFields script has loaded successfully.
\`\`\`javascript
{
  type: 'PAYFIELDS_SCRIPT_LOADED',
  timestamp: '2024-01-01T12:00:00.000Z'
}
\`\`\`

### PAYMENT_SUBMISSION_STARTED
Fired when payment submission begins.
\`\`\`javascript
{
  type: 'PAYMENT_SUBMISSION_STARTED',
  timestamp: '2024-01-01T12:00:00.000Z'
}
\`\`\`

### PAYMENT_SUCCESS
Fired when a payment is successfully processed.
\`\`\`javascript
{
  type: 'PAYMENT_SUCCESS',
  data: {
    // Payment response data from Payrix
    transactionId: 'txn_123456',
    amount: 2500,
    status: 'approved'
  },
  timestamp: '2024-01-01T12:00:00.000Z'
}
\`\`\`

### PAYMENT_FAILURE
Fired when a payment fails.
\`\`\`javascript
{
  type: 'PAYMENT_FAILURE',
  error: 'Payment declined',
  details: { /* Error details */ },
  timestamp: '2024-01-01T12:00:00.000Z'
}
\`\`\`

### PAYMENT_VALIDATION_FAILURE
Fired when payment validation fails (e.g., invalid card details).
\`\`\`javascript
{
  type: 'PAYMENT_VALIDATION_FAILURE',
  error: 'Invalid card number',
  details: { /* Validation error details */ },
  timestamp: '2024-01-01T12:00:00.000Z'
}
\`\`\`

### PAYMENT_REDIRECT
Fired when the payment is successful and a return URL was provided.
\`\`\`javascript
{
  type: 'PAYMENT_REDIRECT',
  url: 'https://yoursite.com/success'
}
\`\`\`

### PAYMENT_TIMEOUT
Fired when payment processing times out.
\`\`\`javascript
{
  type: 'PAYMENT_TIMEOUT',
  error: 'Payment processing timed out',
  timestamp: '2024-01-01T12:00:00.000Z'
}
\`\`\`

## Error Handling

All API endpoints return consistent error responses:

\`\`\`json
{
  "success": false,
  "error": "Error type",
  "message": "Human-readable error message",
  "details": "Additional error details (optional)"
}
\`\`\`

Common HTTP status codes:
- \`200\`: Success
- \`400\`: Bad Request (validation errors)
- \`401\`: Unauthorized (invalid token)
- \`404\`: Not Found (merchant not found)
- \`413\`: Payload Too Large
- \`500\`: Internal Server Error

## Security Considerations

1. **Token Security**: Tokens are cryptographically secure and single-use
2. **CORS**: Configure allowed origins for production
3. **HTTPS**: Always use HTTPS in production
4. **CSP**: Implement Content Security Policy headers
5. **Input Validation**: All inputs are validated server-side
6. **Rate Limiting**: API endpoints include basic rate limiting
`;

  // Define sections for sidebar navigation
  const sections = [
    {
      id: "overview",
      title: "Overview",
      subsections: [
        { id: "what-is-iframe", title: "What is Iframe Integration?" },
        { id: "benefits", title: "Benefits & Features" },
        { id: "security", title: "Security Features" },
      ],
    },
    {
      id: "quickstart",
      title: "Quick Start",
      subsections: [
        { id: "step-1", title: "Step 1: Generate Token" },
        { id: "step-2", title: "Step 2: Embed Iframe" },
        { id: "step-3", title: "Step 3: Handle Events" },
      ],
    },
    {
      id: "examples",
      title: "Code Examples",
      subsections: [
        { id: "html-example", title: "HTML Example" },
        { id: "react-example", title: "React Example" },
        { id: "javascript-example", title: "JavaScript Example" },
      ],
    },
    {
      id: "api",
      title: "API Reference",
      subsections: [
        { id: "token-generation", title: "Token Generation" },
        { id: "iframe-events", title: "Iframe Events" },
        { id: "error-handling", title: "Error Handling" },
      ],
    },
  ];

  return (
    <PageLayout
      title="Iframe Payment Integration"
      subtitle="Embed secure payment forms directly into your website with our iframe integration"
      sections={sections}
      gradientFrom="from-slate-800"
      gradientTo="to-slate-900"
    >
      {/* Overview Section */}
      <ContentCard id="what-is-iframe" title="What is Iframe Integration?" variant="highlight">
        <p className="text-slate-600 text-lg leading-relaxed mb-6">
          Our iframe integration allows you to embed secure payment forms directly into your website without redirecting users to external payment
          pages. This provides a seamless user experience while maintaining PCI compliance and security.
        </p>

        <div className="grid sm:grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-gradient-to-br from-slate-50 to-gray-50 border border-slate-200 rounded-xl p-6 lg:p-8">
            <h3 className="text-lg font-semibold text-slate-800 mb-6 flex items-center">
              <svg className="w-6 h-6 mr-3 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Benefits
            </h3>
            <ul className="space-y-4 text-slate-700">
              {[
                "Seamless user experience - no redirects",
                "PCI compliant - card data never touches your servers",
                "Customizable styling to match your brand",
                "Real-time payment status updates",
                "Mobile responsive design",
                "Secure token-based authentication",
              ].map((benefit, index) => (
                <li key={index} className="flex items-start">
                  <svg className="w-5 h-5 text-slate-600 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="text-sm leading-relaxed">{benefit}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="bg-gradient-to-br from-gray-50 to-slate-50 border border-gray-200 rounded-xl p-6 lg:p-8">
            <h3 className="text-lg font-semibold text-slate-800 mb-6 flex items-center">
              <svg className="w-6 h-6 mr-3 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              How It Works
            </h3>
            <ol className="space-y-4 text-slate-700">
              {[
                "Generate a secure payment token via API",
                "Embed iframe with the token in your page",
                "Customer enters payment details securely",
                "Receive real-time payment status updates",
                "Handle success/failure in your application",
              ].map((step, index) => (
                <li key={index} className="flex items-start">
                  <span className="bg-slate-800 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0 font-semibold">
                    {index + 1}
                  </span>
                  <span className="text-sm leading-relaxed">{step}</span>
                </li>
              ))}
            </ol>
          </div>
        </div>
      </ContentCard>
      {/* Security Section */}
      <ContentCard id="security" title="Security Features" variant="warning">
        <p className="text-slate-700 mb-6 leading-relaxed">
          Our iframe integration is designed with security as a top priority. All sensitive payment data is processed within our secure environment
          and never touches your servers.
        </p>
        <div className="grid sm:grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white/50 rounded-lg p-6">
            <h4 className="font-semibold text-slate-800 mb-4 flex items-center">
              <svg className="w-5 h-5 mr-3 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"
                />
              </svg>
              Token Security
            </h4>
            <ul className="space-y-3 text-slate-700">
              {["Cryptographically secure tokens", "Configurable expiration times", "Single-use token validation"].map((item, index) => (
                <li key={index} className="flex items-start">
                  <svg className="w-4 h-4 text-slate-600 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="text-sm leading-relaxed">{item}</span>
                </li>
              ))}
            </ul>
          </div>
          <div className="bg-white/50 rounded-lg p-6">
            <h4 className="font-semibold text-slate-800 mb-4 flex items-center">
              <svg className="w-5 h-5 mr-3 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                />
              </svg>
              PCI Compliance
            </h4>
            <ul className="space-y-3 text-slate-700">
              {["Card data processed in secure iframes", "No sensitive data on your servers", "Payrix handles PCI compliance"].map((item, index) => (
                <li key={index} className="flex items-start">
                  <svg className="w-4 h-4 text-slate-600 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span className="text-sm leading-relaxed">{item}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </ContentCard>

      {/* Quick Start Section */}
      <ContentCard id="step-1" title="Step 1: Generate Payment Token" variant="highlight">
        <p className="text-slate-600 mb-4">Make a POST request to generate a secure payment token:</p>
        <div className="bg-slate-900 text-slate-300 p-6 rounded-lg font-mono text-sm overflow-x-auto">
          <pre className="text-slate-200">{`curl -X POST https://your-api-domain.com/payments/generate-integration-token \\
  -H "Content-Type: application/json" \\
  -d '{
    "merchantId": "your-merchant-id",
    "description": "Product Purchase",
    "amount": 2500,
    "returnUrl": "https://yoursite.com/success"
  }'`}</pre>
        </div>
      </ContentCard>

      <ContentCard id="step-2" title="Step 2: Embed Iframe" variant="success">
        <p className="text-slate-600 mb-4">Use the returned embedUrl to create an iframe:</p>
        <div className="bg-slate-900 text-slate-300 p-6 rounded-lg font-mono text-sm overflow-x-auto">
          <pre className="text-slate-200">{`<iframe
  src="https://your-domain.com/payment-iframe?token=..."
  width="100%"
  height="600"
  frameborder="0"
  allow="payment">
</iframe>`}</pre>
        </div>
      </ContentCard>

      <ContentCard id="step-3" title="Step 3: Handle Events">
        <p className="text-slate-600 mb-4">Listen for payment events from the iframe:</p>
        <div className="bg-slate-900 text-slate-300 p-6 rounded-lg font-mono text-sm overflow-x-auto">
          <pre className="text-slate-200">{`window.addEventListener('message', (event) => {
  if (event.data.type === 'PAYMENT_SUCCESS') {
    console.log('Payment successful!', event.data.data);
    // Handle success
  } else if (event.data.type === 'PAYMENT_FAILURE') {
    console.log('Payment failed:', event.data.error);
    // Handle failure
  }
});`}</pre>
        </div>
      </ContentCard>

      {/* Code Examples Section */}
      <ContentCard id="html-example" title="Complete HTML Example">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
          <p className="text-slate-600 leading-relaxed">A complete HTML page with iframe integration:</p>
          <button
            onClick={() => copyToClipboard(exampleHtml, "HTML Example")}
            className="px-4 py-2 bg-slate-800 text-white text-sm rounded-lg hover:bg-slate-900 transition-colors flex-shrink-0 self-start sm:self-auto"
          >
            {copied === "HTML Example" ? "Copied!" : "Copy Code"}
          </button>
        </div>
        <div className="bg-slate-900 text-slate-100 p-4 sm:p-6 rounded-lg text-sm overflow-x-auto max-h-96 overflow-y-auto border">
          <pre className="whitespace-pre-wrap sm:whitespace-pre">{exampleHtml}</pre>
        </div>
      </ContentCard>

      <ContentCard id="react-example" title="React/JavaScript Example">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
          <p className="text-slate-600 leading-relaxed">React component with iframe integration:</p>
          <button
            onClick={() => copyToClipboard(jsExample, "React Example")}
            className="px-4 py-2 bg-slate-800 text-white text-sm rounded-lg hover:bg-slate-900 transition-colors flex-shrink-0 self-start sm:self-auto"
          >
            {copied === "React Example" ? "Copied!" : "Copy Code"}
          </button>
        </div>
        <div className="bg-slate-900 text-slate-100 p-4 sm:p-6 rounded-lg text-sm overflow-x-auto max-h-96 overflow-y-auto border">
          <pre className="whitespace-pre-wrap sm:whitespace-pre">{jsExample}</pre>
        </div>
      </ContentCard>

      {/* API Reference Section */}
      <ContentCard id="api-reference" title="API Reference">
        <p className="text-slate-600 mb-6 leading-relaxed">Complete API documentation for iframe integration endpoints and events:</p>
        <div className="bg-slate-900 text-slate-100 p-4 sm:p-6 rounded-lg text-sm overflow-x-auto max-h-96 overflow-y-auto border">
          <pre className="whitespace-pre-wrap sm:whitespace-pre">{apiDocs}</pre>
        </div>
      </ContentCard>
    </PageLayout>
  );
};

export default IframeIntegrationDocs;
