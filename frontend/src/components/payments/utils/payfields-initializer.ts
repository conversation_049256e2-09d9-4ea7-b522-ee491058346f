import { PayFieldsConfig, BillingAddress } from '../types/payfields.types';
import { isInIframe } from './iframe-communication';

interface InitializeOptions {
  onSuccess: (response: unknown) => void;
  onFailure: (error: unknown) => void;
  onValidationFailure: (error: unknown) => void;
  onFinish: (response: unknown) => void;
  billingAddress?: BillingAddress;
}

export const validatePayFieldsConfig = (config: PayFieldsConfig): void => {
  console.log("Validating PayFields configuration:", {
    hasPublicKey: !!config.publicKey,
    publicKeyLength: config.publicKey?.length || 0,
    publicKeyFormat: config.publicKey ? (config.publicKey.startsWith("pk_") ? "Valid format" : "Invalid format") : "Missing",
    hasMerchantId: !!config.merchantId,
    merchantId: config.merchantId,
    mode: config.mode,
    txnType: config.txnType,
    amount: config.amount,
    description: config.description,
  });

  if (!config.publicKey || !config.merchantId) {
    throw new Error(`Missing required PayFields configuration: ${!config.publicKey ? "publicKey" : "merchantId"}`);
  }

  if (!config.publicKey.startsWith("pk_")) {
    console.warn("⚠️ PublicKey format warning: Should start with 'pk_' for live keys or might be a session key");
  }

  console.log("✅ Configuration validation passed");
};

export const checkDOMElements = (): boolean => {
  const cardNumberEl = document.getElementById("card-number");
  const cardNameEl = document.getElementById("card-name");
  const cardCvvEl = document.getElementById("card-cvv");
  const cardExpirationEl = document.getElementById("card-expiration");

  if (!cardNumberEl || !cardNameEl || !cardCvvEl || !cardExpirationEl) {
    console.error("PayFields DOM elements not found:", {
      cardNumber: !!cardNumberEl,
      cardName: !!cardNameEl,
      cardCvv: !!cardCvvEl,
      cardExpiration: !!cardExpirationEl,
    });
    return false;
  }

  console.log("DOM elements verified successfully");
  return true;
};

export const configurePayFields = (config: PayFieldsConfig, billingAddress?: BillingAddress): void => {
  if (!window.PayFields) {
    throw new Error("PayFields not loaded");
  }

  window.PayFields.config.apiKey = config.publicKey;
  window.PayFields.config.merchant = config.merchantId;
  window.PayFields.config.mode = config.mode;
  window.PayFields.config.txnType = config.txnType;
  window.PayFields.config.description = config.description;

  if (config.mode === "token") {
    window.PayFields.config.amount = 0;
    if (config.txnType !== "auth") {
      console.warn("Token-only mode typically uses auth transaction type. Current type:", config.txnType);
    }
  } else {
    window.PayFields.config.amount = config.amount;
  }

  if (isInIframe()) {
    window.PayFields.config.iframe = true;
    window.PayFields.config.responsive = true;
    window.PayFields.config.autoResize = true;
  }

  if (billingAddress) {
    window.PayFields.config.billing = {
      name: `${billingAddress.firstName} ${billingAddress.lastName}`,
      email: billingAddress.email,
      phone: billingAddress.phone,
      address: billingAddress.line1,
      address2: billingAddress.line2,
      city: billingAddress.city,
      state: billingAddress.state,
      zip: billingAddress.zip,
      country: billingAddress.country,
    };
    window.PayFields.config.name = `${billingAddress.firstName} ${billingAddress.lastName}`;
  }
};

export const setupPayFieldsElements = (): void => {
  if (!window.PayFields) return;

  const fields = [
    { type: "number", element: "#card-number" },
    { type: "name", element: "#card-name" },
    { type: "cvv", element: "#card-cvv" },
    { type: "expiration", element: "#card-expiration" },
  ];

  window.PayFields.fields = fields;
};

export const applyPayFieldsStyles = (): void => {
  if (!window.PayFields) return;

  const baseInputStyle = isInIframe() 
    ? {
        fontSize: "16px",
        padding: "0.625rem 0.875rem",
      }
    : {
        fontSize: "0.875rem",
        padding: "0.75rem 1rem",
      };

  window.PayFields.customizations.style = {
    ".input": {
      display: "block",
      width: "100%",
      padding: baseInputStyle.padding,
      fontSize: baseInputStyle.fontSize,
      fontWeight: "400",
      lineHeight: "1.5rem",
      backgroundColor: "#fff",
      border: "1px solid #e2e8f0",
      borderRadius: "0.375rem",
      appearance: "none",
    },
    ".form-error": {
      color: "#e53e3e",
      fontSize: "0.75rem",
      marginTop: "0.25rem",
    },
  };

  window.PayFields.customizations.placeholders = {
    "#expiration": "MM/YY",
    "#payment_cvv": "CVV",
    "#payment_number": "0000 0000 0000 0000",
    "#name": "Full Name on Card",
  };
};

export const initializePayFields = (config: PayFieldsConfig, options: InitializeOptions): void => {
  try {
    if (!checkDOMElements()) {
      throw new Error("Required payment form elements not found in DOM");
    }

    validatePayFieldsConfig(config);
    configurePayFields(config, options.billingAddress);
    setupPayFieldsElements();
    applyPayFieldsStyles();

    window.PayFields.onSuccess = options.onSuccess;
    window.PayFields.onFailure = options.onFailure;
    window.PayFields.onValidationFailure = options.onValidationFailure;
    window.PayFields.onFinish = options.onFinish;

    console.log("Calling PayFields.ready() with configuration:", {
      apiKey: config.publicKey ? "***" + config.publicKey.slice(-4) : "NOT_SET",
      merchant: window.PayFields.config.merchant,
      mode: window.PayFields.config.mode,
      txnType: window.PayFields.config.txnType,
      amount: window.PayFields.config.amount,
      fieldsCount: window.PayFields.fields?.length || 0,
    });

    window.PayFields.ready();
    console.log("PayFields initialization called - checking if fields render properly...");
  } catch (error) {
    console.error("Error initializing PayFields:", error);
    throw error;
  }
};

export const updateBillingAddress = (billingAddress: BillingAddress): void => {
  if (!window.PayFields || !billingAddress) return;

  console.log("Updating billing address without re-initialization");

  window.PayFields.config.billing = {
    name: `${billingAddress.firstName} ${billingAddress.lastName}`,
    email: billingAddress.email,
    phone: billingAddress.phone,
    address: billingAddress.line1,
    address2: billingAddress.line2,
    city: billingAddress.city,
    state: billingAddress.state,
    zip: billingAddress.zip,
    country: billingAddress.country,
  };

  window.PayFields.config.name = `${billingAddress.firstName} ${billingAddress.lastName}`;
};