import { PaymentMessage, PaymentMessageType } from '../types/payfields.types';

export const isInIframe = (): boolean => {
  return window.self !== window.top;
};

export const postMessageToParent = (
  type: PaymentMessageType,
  payload: Partial<Omit<PaymentMessage, 'type' | 'timestamp'>> = {}
): void => {
  if (!isInIframe() || window.parent === window) return;

  const message: PaymentMessage = {
    type,
    timestamp: new Date().toISOString(),
    ...payload
  };

  window.parent.postMessage(message, "*");
};