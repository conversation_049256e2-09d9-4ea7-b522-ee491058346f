import { toast } from "sonner";
import { processTokenPayment } from "../../../services/api";
import { PayFieldsConfig, BillingAddress, PaymentResponse, PaymentError } from '../types/payfields.types';
import { postMessageToParent } from '../utils/iframe-communication';

export const createPaymentSuccessHandler = (
  config: PayFieldsConfig,
  billingAddress?: BillingAddress,
  onSuccess?: (response: unknown) => void
) => {
  return async (response: PaymentResponse) => {
    console.log("Payment successful:", response);

    if (config.mode === "token" && response.token) {
      console.log("Token created, processing payment:", {
        token: response.token.substring(0, 8) + "...",
        merchantId: config.merchantId,
        amount: config.amount,
      });

      try {
        const paymentResult = await processTokenPayment({
          merchantId: config.merchantId,
          token: response.token,
          amount: config.amount,
          description: config.description,
          customerInfo: billingAddress
            ? {
                name: `${billingAddress.firstName} ${billingAddress.lastName}`,
                email: billingAddress.email,
                address: {
                  line1: billingAddress.line1,
                  line2: billingAddress.line2,
                  city: billingAddress.city,
                  state: billingAddress.state,
                  zip: billingAddress.zip,
                  country: billingAddress.country,
                },
              }
            : undefined,
        });

        if (paymentResult.success) {
          toast.success("Payment processed successfully!");
          console.log("Token payment completed:", paymentResult.transaction);

          postMessageToParent("PAYMENT_SUCCESS", {
            data: {
              ...response,
              transaction: paymentResult.transaction,
              merchantInfo: paymentResult.merchantInfo,
            },
          });

          if (onSuccess) {
            onSuccess({
              ...response,
              transaction: paymentResult.transaction,
              merchantInfo: paymentResult.merchantInfo,
            });
          }
        } else {
          throw new Error(paymentResult.message || "Token payment processing failed");
        }
      } catch (error) {
        console.error("Token payment processing failed:", error);
        const errorMessage = error instanceof Error ? error.message : "Token payment processing failed";
        toast.error(errorMessage);

        postMessageToParent("PAYMENT_ERROR", { error: errorMessage });

        if (onSuccess) {
          onSuccess({ message: errorMessage });
        }
        throw error;
      }
    } else {
      toast.success("Payment processed successfully!");
      postMessageToParent("PAYMENT_SUCCESS", { data: response });
      if (onSuccess) onSuccess(response);
    }
  };
};

export const createPaymentFailureHandler = (onFailure?: (error: unknown) => void) => {
  return (err: PaymentError) => {
    console.error("Payment failed:", err);

    let errorMessage = "Payment processing failed. Please try again.";
    if (err && Array.isArray(err.errors)) {
      const fieldErrors = err.errors.map((e) => `${e.field}: ${e.msg}`).join(", ");
      errorMessage = `Payment failed: ${fieldErrors}`;
    } else if (err && err.message) {
      errorMessage = err.message;
    }

    toast.error(errorMessage);
    postMessageToParent("PAYMENT_FAILURE", {
      error: errorMessage,
      details: err,
    });

    if (onFailure) onFailure(err);
  };
};

export const createValidationFailureHandler = (onFailure?: (error: unknown) => void) => {
  return (err: unknown) => {
    console.log("Validation error:", err);

    const validationMessage = "Payment validation failed. Please check your card details.";
    toast.error("Please check your card details");

    postMessageToParent("PAYMENT_VALIDATION_FAILURE", {
      error: validationMessage,
      details: err,
    });

    if (onFailure) onFailure({ message: validationMessage, details: err });
  };
};

export const createPaymentFinishHandler = () => {
  return (response: unknown) => {
    console.log("PayFields finished:", response);
    postMessageToParent("PAYMENT_FINISHED", { data: response });
  };
};