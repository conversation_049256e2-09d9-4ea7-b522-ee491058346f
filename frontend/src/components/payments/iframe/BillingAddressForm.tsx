import type { BillingAddress } from "../../../types/payment";
import { AddressFields } from "./AddressFields";
import { SecurityInfoSection } from "./SecurityInfoSection";

interface BillingAddressFormProps {
  billingAddress: BillingAddress;
  termsAccepted: boolean;
  handleAddressChange: (field: keyof BillingAddress, value: string) => void;
  setTermsAccepted: (accepted: boolean) => void;
}

export const BillingAddressForm = ({ 
  billingAddress, 
  termsAccepted, 
  handleAddressChange, 
  setTermsAccepted 
}: BillingAddressFormProps) => {
  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold text-gray-900 mb-3">Billing Address</h2>

      <AddressFields 
        billingAddress={billingAddress} 
        handleAddressChange={handleAddressChange} 
      />

      {/* Compact Terms and Conditions */}
      <div className="pt-3 border-t border-gray-100">
        <div className="flex items-start space-x-2 min-h-[44px]">
          <input
            type="checkbox"
            id="terms"
            checked={termsAccepted}
            onChange={(e) => setTermsAccepted(e.target.checked)}
            className="mt-0.5 h-5 w-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
            aria-describedby="terms-description"
          />
          <label htmlFor="terms" className="flex-1 py-2 cursor-pointer text-xs text-gray-700 leading-relaxed" id="terms-description">
            I agree to the{" "}
            <button
              type="button"
              className="text-blue-600 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded"
              onClick={() => console.log('Open terms modal')}
            >
              terms
            </button>
            {", "}
            <button
              type="button"
              className="text-blue-600 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded"
              onClick={() => console.log('Open privacy modal')}
            >
              privacy policy
            </button>
            {", and "}
            <button
              type="button"
              className="text-blue-600 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded"
              onClick={() => console.log('Open refund modal')}
            >
              refund policy
            </button>
            .
          </label>
        </div>
      </div>

      <SecurityInfoSection />
    </div>
  );
};