import type { PaymentInfo } from "../../../types/payment";
import { calculateSubtotal, formatDate } from "../../../utils/paymentUtils";

interface OrderOverviewProps {
  paymentInfo: PaymentInfo;
}

export const OrderOverview = ({ paymentInfo }: OrderOverviewProps) => {
  // const orderNumber = getDisplayOrderNumber(paymentInfo);
  const date = formatDate();
  const taxAmount = paymentInfo.taxAmount || 0;
  const subtotal = calculateSubtotal(paymentInfo.amount, taxAmount);

  return (
    <div className="mb-6 p-4 bg-gray-50 rounded-lg">
      <div className="space-y-2">
        {/* <div className="flex justify-between items-center"> */}
        {/* <span className="text-sm text-gray-600">Order Number</span> */}
        {/* <span className="text-sm font-medium text-gray-900">{orderNumber}</span> */}
        {/* </div> */}
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Date</span>
          <span className="text-sm text-gray-900">{date}</span>
        </div>
        <div className="border-t border-gray-200 pt-2 mt-2">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Subtotal</span>
            <span className="text-sm text-gray-900">${(subtotal / 100).toFixed(2)}</span>
          </div>
          {taxAmount > 0 && (
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Tax</span>
              <span className="text-sm text-gray-900">${(taxAmount / 100).toFixed(2)}</span>
            </div>
          )}
          <div className="flex justify-between items-center font-semibold border-t border-gray-200 pt-2 mt-2">
            <span className="text-gray-900">Total</span>
            <span className="text-gray-900">
              ${(paymentInfo.amount / 100).toFixed(2)} {paymentInfo.currency || "USD"}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
