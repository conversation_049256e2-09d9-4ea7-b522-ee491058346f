import type { BillingAddress } from "../../../types/payment";
import { US_STATES } from "../../../data/states";

interface AddressFieldsProps {
  billingAddress: BillingAddress;
  handleAddressChange: (field: keyof BillingAddress, value: string) => void;
}

export const AddressFields = ({ billingAddress, handleAddressChange }: AddressFieldsProps) => {
  const inputClasses = "w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm";
  const labelClasses = "block text-xs font-medium text-gray-700 mb-1";

  return (
    <div className="space-y-3">
      {/* Name Row */}
      <div className="grid grid-cols-2 gap-3">
        <div>
          <label htmlFor="firstName" className={labelClasses}>
            First Name <span className="text-red-500" aria-label="required">*</span>
          </label>
          <input
            id="firstName"
            type="text"
            value={billingAddress.firstName}
            onChange={(e) => handleAddressChange("firstName", e.target.value)}
            className={inputClasses}
            placeholder="First name"
            required
            aria-required="true"
          />
        </div>
        <div>
          <label htmlFor="lastName" className={labelClasses}>
            Last Name <span className="text-red-500" aria-label="required">*</span>
          </label>
          <input
            id="lastName"
            type="text"
            value={billingAddress.lastName}
            onChange={(e) => handleAddressChange("lastName", e.target.value)}
            className={inputClasses}
            placeholder="Last name"
            required
            aria-required="true"
          />
        </div>
      </div>

      {/* Contact Row */}
      <div className="grid grid-cols-2 gap-3">
        <div>
          <label htmlFor="email" className={labelClasses}>
            Email <span className="text-red-500" aria-label="required">*</span>
          </label>
          <input
            id="email"
            type="email"
            value={billingAddress.email}
            onChange={(e) => handleAddressChange("email", e.target.value)}
            className={inputClasses}
            placeholder="Email"
            required
            aria-required="true"
          />
        </div>
        <div>
          <label htmlFor="phone" className={labelClasses}>Phone</label>
          <input
            id="phone"
            type="tel"
            value={billingAddress.phone}
            onChange={(e) => handleAddressChange("phone", e.target.value)}
            className={inputClasses}
            placeholder="Phone"
          />
        </div>
      </div>

      {/* Address Row */}
      <div className="grid grid-cols-3 gap-3">
        <div className="col-span-2">
          <label htmlFor="address1" className={labelClasses}>
            Address <span className="text-red-500" aria-label="required">*</span>
          </label>
          <input
            id="address1"
            type="text"
            value={billingAddress.line1}
            onChange={(e) => handleAddressChange("line1", e.target.value)}
            className={inputClasses}
            placeholder="Street address"
            required
            aria-required="true"
          />
        </div>
        <div>
          <label htmlFor="address2" className={labelClasses}>Apt/Unit</label>
          <input
            id="address2"
            type="text"
            value={billingAddress.line2}
            onChange={(e) => handleAddressChange("line2", e.target.value)}
            className={inputClasses}
            placeholder="Apt/Unit"
          />
        </div>
      </div>

      {/* Location Row */}
      <div className="grid grid-cols-4 gap-3">
        <div className="col-span-2">
          <label htmlFor="city" className={labelClasses}>
            City <span className="text-red-500" aria-label="required">*</span>
          </label>
          <input
            id="city"
            type="text"
            value={billingAddress.city}
            onChange={(e) => handleAddressChange("city", e.target.value)}
            className={inputClasses}
            placeholder="City"
            required
            aria-required="true"
          />
        </div>
        <div>
          <label htmlFor="state" className={labelClasses}>
            State <span className="text-red-500" aria-label="required">*</span>
          </label>
          <select
            id="state"
            value={billingAddress.state}
            onChange={(e) => handleAddressChange("state", e.target.value)}
            className={inputClasses}
            required
            aria-required="true"
          >
            <option value="">State</option>
            {US_STATES.map((state) => (
              <option key={state.code} value={state.code}>
                {state.code}
              </option>
            ))}
          </select>
        </div>
        <div>
          <label htmlFor="zip" className={labelClasses}>
            ZIP <span className="text-red-500" aria-label="required">*</span>
          </label>
          <input
            id="zip"
            type="text"
            value={billingAddress.zip}
            onChange={(e) => handleAddressChange("zip", e.target.value)}
            className={inputClasses}
            placeholder="ZIP"
            required
            aria-required="true"
          />
        </div>
      </div>

      {/* Country (Hidden since US only, but maintained for data consistency) */}
      <input
        type="hidden"
        value="US"
        onChange={(e) => handleAddressChange("country", e.target.value)}
      />
    </div>
  );
};