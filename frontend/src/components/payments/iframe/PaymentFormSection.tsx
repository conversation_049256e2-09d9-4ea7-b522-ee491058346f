import PaymentLogos from "../PaymentLogos";
import SecurePayFields from "../SecurePayFields";
import type { PayFieldsConfig, BillingAddress } from "../../../types/payment";
import { PaymentValidation } from "./PaymentValidation";

interface PaymentFormSectionProps {
  payFieldsConfig: PayFieldsConfig | null;
  billingAddress: BillingAddress;
  error: string | null;
  isAddressValid: boolean;
  onSuccess: (response: unknown) => void;
  onFailure: (error: unknown) => void;
}

export const PaymentFormSection = ({ 
  payFieldsConfig, 
  billingAddress, 
  error, 
  isAddressValid, 
  onSuccess, 
  onFailure 
}: PaymentFormSectionProps) => {
  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-4">Payment Information</h2>

      {/* Accepted cards */}
      <div className="mb-4">
        <p className="text-sm text-gray-600 mb-2">Accepted Payment Methods</p>
        <PaymentLogos />
      </div>

      <PaymentValidation error={error} isAddressValid={isAddressValid} />

      {payFieldsConfig && (
        <div className={!isAddressValid ? "opacity-50 pointer-events-none" : ""}>
          <SecurePayFields
            config={payFieldsConfig}
            onSuccess={onSuccess}
            onFailure={onFailure}
            billingAddress={billingAddress}
          />
        </div>
      )}

      {/* Save card option */}
      <div className="mt-4">
        <label className="flex items-center text-sm text-gray-600">
          <input 
            type="checkbox" 
            className="mr-2 h-4 w-4 text-[#364F6B] border-gray-300 rounded focus:ring-[#364F6B]" 
          />
          Save card for future payments
        </label>
      </div>

      {/* Payment note */}
      <div className="mt-4 text-xs text-gray-500 text-center">
        <p>Payment processed securely. Fill in the form and click &quot;Pay Now&quot; to proceed.</p>
      </div>
    </div>
  );
};