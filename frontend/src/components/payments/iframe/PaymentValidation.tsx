interface PaymentValidationProps {
  error: string | null;
  isAddressValid: boolean;
}

export const PaymentValidation = ({ error, isAddressValid }: PaymentValidationProps) => {
  return (
    <>
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-700">{error}</p>
        </div>
      )}

      {!isAddressValid && (
        <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-md">
          <p className="text-sm text-amber-700 text-center">
            Please complete billing information and accept terms to continue
          </p>
        </div>
      )}
    </>
  );
};