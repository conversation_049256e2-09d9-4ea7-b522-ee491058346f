import type { PaymentInfo } from "../../../types/payment";

interface SuccessStateProps {
  paymentInfo: PaymentInfo | null;
}

export const SuccessState = ({ paymentInfo }: SuccessStateProps) => {
  return (
    <div className="min-h-screen bg-slate-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full">
        <div className="bg-white rounded-lg shadow-sm border border-green-200 p-6">
          <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mx-auto mb-4">
            <svg className="w-6 h-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h1 className="text-xl font-semibold text-gray-900 text-center mb-2">Payment Successful!</h1>
          <p className="text-gray-600 text-center">Your payment has been processed successfully.</p>
          {paymentInfo?.returnUrl && (
            <p className="text-gray-500 text-sm text-center mt-4">Redirecting you back...</p>
          )}
        </div>
      </div>
    </div>
  );
};