import type { MerchantInfo as MerchantInfoType } from "../../../types/payment";

interface MerchantInfoProps {
  merchantInfo: MerchantInfoType;
}

export const MerchantInfo = ({ merchantInfo }: MerchantInfoProps) => {
  return (
    <div className="space-y-4">
      <div>
        <p className="text-sm text-gray-500">Merchant Information</p>
        <p className="font-medium text-gray-900">{merchantInfo.name}</p>
        {merchantInfo.address && (
          <div className="text-sm text-gray-600 mt-1">
            {merchantInfo.address.line1 && <p>{merchantInfo.address.line1}</p>}
            {merchantInfo.address.line2 && <p>{merchantInfo.address.line2}</p>}
            <p>
              {merchantInfo.address.city && `${merchantInfo.address.city}, `}
              {merchantInfo.address.state} {merchantInfo.address.zip}
            </p>
            {merchantInfo.address.country && <p>{merchantInfo.address.country}</p>}
          </div>
        )}
        {merchantInfo.contactEmail && (
          <p className="text-sm text-gray-600 mt-1">
            Contact:{" "}
            <a href={`mailto:${merchantInfo.contactEmail}`} className="text-blue-600">
              {merchantInfo.contactEmail}
            </a>
          </p>
        )}
        {merchantInfo.contactPhone && (
          <p className="text-sm text-gray-600">Phone: {merchantInfo.contactPhone}</p>
        )}
      </div>
    </div>
  );
};