import type { MerchantInfo as MerchantInfoType, PaymentInfo } from "../../../types/payment";
import { OrderOverview } from "./OrderOverview";
import { MerchantInfo } from "./MerchantInfo";
import { PoweredBySection } from "./PoweredBySection";

interface OrderSummaryProps {
  paymentInfo: PaymentInfo | null;
  merchantInfo: MerchantInfoType | null;
}

export const OrderSummary = ({ paymentInfo, merchantInfo }: OrderSummaryProps) => {
  return (
    <div className="space-y-4">
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-3">Order Summary</h2>
        
        {/* Order Overview */}
        {paymentInfo && <OrderOverview paymentInfo={paymentInfo} />}
        
        {/* Merchant Information - Progressive disclosure */}
        {merchantInfo && (
          <details className="group mt-3 focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2 rounded">
            <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900 flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded min-h-[44px] py-2">
              <svg className="w-4 h-4 mr-1 transition-transform group-open:rotate-90" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
              </svg>
              Merchant Details
            </summary>
            <div className="mt-2">
              <MerchantInfo merchantInfo={merchantInfo} />
            </div>
          </details>
        )}
      </div>

      {/* Powered by section - Minimized */}
      <PoweredBySection />
    </div>
  );
};