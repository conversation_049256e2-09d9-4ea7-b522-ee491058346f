import visaLogo from '/paymentLogos/visa.png';
import mastercardLogo from '/paymentLogos/mastercard.png';
import amexLogo from '/paymentLogos/amex.png';
import discoverLogo from '/paymentLogos/discover.png';

const PaymentLogos = ({ className = "" }: { className?: string }) => {
  return (
    <div className={`flex flex-wrap items-center gap-2 ${className}`}>
      {/* Visa */}
      <div className="w-10 h-7 bg-white rounded shadow-sm flex items-center justify-center p-1 border border-gray-100">
        <img src={visaLogo} alt="Visa" className="w-full h-auto object-contain" />
      </div>

      {/* Mastercard */}
      <div className="w-10 h-7 bg-white rounded shadow-sm flex items-center justify-center p-1 border border-gray-100">
        <img src={mastercardLogo} alt="Mastercard" className="w-full h-auto object-contain" />
      </div>

      {/* American Express */}
      <div className="w-10 h-7 bg-white rounded shadow-sm flex items-center justify-center p-1 border border-gray-100">
        <img src={amexLogo} alt="American Express" className="w-full h-auto object-contain" />
      </div>

      {/* Discover */}
      <div className="w-10 h-7 bg-white rounded shadow-sm flex items-center justify-center p-1 border border-gray-100">
        <img src={discoverLogo} alt="Discover" className="w-full h-auto object-contain" />
      </div>
    </div>
  );
};

export default PaymentLogos;