const LoadingSpinner = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
      <div className="flex flex-col items-center space-y-4">
        <div className="relative">
          <div className="w-16 h-16 border-4 border-blue-200 rounded-full animate-spin border-t-blue-600"></div>
          <div className="w-12 h-12 border-4 border-indigo-200 rounded-full animate-ping absolute top-2 left-2 border-t-indigo-500"></div>
        </div>
        <div className="text-slate-600 font-medium text-lg">Loading...</div>
        <div className="text-slate-400 text-sm">Please wait while we prepare your experience</div>
      </div>
    </div>
  );
};

export default LoadingSpinner;