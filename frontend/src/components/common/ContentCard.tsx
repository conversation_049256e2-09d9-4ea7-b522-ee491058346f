import { type ReactNode } from "react";

interface ContentCardProps {
  id?: string;
  title?: string;
  children: ReactNode;
  className?: string;
  variant?: "default" | "highlight" | "success" | "warning";
  noPadding?: boolean;
}

export const ContentCard = ({ id, title, children, className = "", variant = "default", noPadding = false }: ContentCardProps) => {
  const variantStyles = {
    default: "bg-white border-slate-200",
    highlight: "bg-gradient-to-br from-slate-50 to-gray-50 border-slate-300",
    success: "bg-gradient-to-br from-slate-50 to-gray-100 border-slate-300",
    warning: "bg-gradient-to-br from-gray-50 to-slate-100 border-gray-300",
  };

  const titleStyles = {
    default: "text-slate-800",
    highlight: "text-slate-900",
    success: "text-slate-900",
    warning: "text-gray-900",
  };

  return (
    <div
      id={id}
      className={`rounded-xl shadow-lg border ${variantStyles[variant]} overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1 ${className}`}
    >
      {title && (
        <div className={`px-6 py-4 border-b border-slate-200/50 ${noPadding ? "" : "bg-white/50"}`}>
          <h2 className={`text-xl font-semibold ${titleStyles[variant]}`}>{title}</h2>
        </div>
      )}
      <div className={noPadding ? "" : "p-6"}>{children}</div>
    </div>
  );
};
