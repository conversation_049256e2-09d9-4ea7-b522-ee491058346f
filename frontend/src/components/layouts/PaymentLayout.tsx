import type { ReactNode } from "react";
import PaymentHeader from "../payments/PaymentHeader";
import { PaymentFooter } from "../payments/iframe/PaymentFooter";

interface PaymentLayoutProps {
  children: ReactNode;
}

export const PaymentLayout = ({ children }: PaymentLayoutProps) => {
  return (
    <div className="h-screen bg-slate-50 grid grid-rows-[auto_1fr_auto] overflow-hidden">
      {/* Header with logo - Fixed height */}
      <PaymentHeader />
      
      {/* Main content area - Flexible height */}
      <div className="flex flex-col overflow-hidden">
        {/* Main card container - Takes remaining space */}
        <div className="flex-1 flex flex-col p-2 sm:p-4 lg:p-6 max-w-7xl mx-auto w-full">
          <div className="flex-1 bg-white rounded-lg shadow-lg overflow-hidden grid grid-rows-[auto_1fr]">
            {/* Gradient bar and title - Fixed height */}
            <div>
              <div className="h-2 bg-gradient-to-r from-[#364F6B] to-[#CBB26B]"></div>
              <div className="p-3 sm:p-4 lg:p-6 border-b border-gray-100">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-center text-gray-900">
                  Secure Checkout
                </h1>
              </div>
            </div>
            
            {/* Content area - Flexible height with responsive spacing */}
            <div className="flex-1 overflow-auto">
              <div className="p-2 sm:p-3 md:p-4 lg:p-6 h-full">
                <div className="space-y-3 sm:space-y-4 lg:space-y-6">
                  {children}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer - Fixed height */}
      <PaymentFooter />
    </div>
  );
};