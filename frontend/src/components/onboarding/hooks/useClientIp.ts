import { useState, useEffect } from "react";

export const useClientIp = (): string => {
  const [clientIp, setClientIp] = useState<string>("127.0.0.1");

  useEffect(() => {
    const getClientIp = async () => {
      try {
        const response = await fetch("https://api.ipify.org?format=json");
        const data = await response.json();
        setClientIp(data.ip);
      } catch (error) {
        console.warn("Failed to get client IP, using default:", error);
        // Keep default 127.0.0.1
      }
    };

    getClientIp();
  }, []);

  return clientIp;
};
