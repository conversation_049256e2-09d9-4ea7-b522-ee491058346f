import React, { useState } from "react";
import { type AxiosError } from "axios";
import { toast } from "sonner";
import { createMerchant, type CreatePayrixMerchantRequest } from "../../../services/api";
import { transformFormDataToPayrix } from "../utils/payrixDataTransform";
import { validateComplianceCheckboxes, hasValidationErrors } from "../utils/reviewValidation";
import { type ComplianceState, type ComplianceValidationErrors } from "../utils/reviewValidation";
import { type SubmitResult, type SubmissionState } from "../types/reviewTypes";

type FormData = Partial<CreatePayrixMerchantRequest>;

interface UseSubmitOnboardingProps {
  formData: FormData;
  complianceState: ComplianceState;
  clientIp: string;
}

interface UseSubmitOnboardingReturn extends SubmissionState {
  validationErrors: ComplianceValidationErrors;
  handleSubmit: () => Promise<void>;
  setValidationErrors: React.Dispatch<React.SetStateAction<ComplianceValidationErrors>>;
}

export const useSubmitOnboarding = ({ formData, complianceState, clientIp }: UseSubmitOnboardingProps): UseSubmitOnboardingReturn => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitResult, setSubmitResult] = useState<SubmitResult | null>(null);
  const [validationErrors, setValidationErrors] = useState<ComplianceValidationErrors>({});

  const handleSubmit = async () => {
    // Clear previous validation errors
    setValidationErrors({});

    // Validate compliance checkboxes
    const errors = validateComplianceCheckboxes(complianceState);

    if (hasValidationErrors(errors)) {
      setValidationErrors(errors);
      toast.error("Please accept all required terms and disclosures before submitting.");
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Transform form data to Payrix format
      const merchantData = transformFormDataToPayrix(formData, complianceState, clientIp);

      const result = await createMerchant(merchantData);

      console.log("Onboarding submitted successfully:", result);
      toast.success("Merchant onboarding completed successfully!", {
        description: "Your application has been submitted for review.",
      });

      setSubmitResult(result as unknown as SubmitResult);
      setSubmitSuccess(true);
    } catch (error) {
      console.error("Submission error:", error);
      const axiosError = error as AxiosError<{ message?: string; error?: string }>;
      const message = axiosError.response?.data?.message || axiosError.response?.data?.error || axiosError.message || "An unexpected error occurred";
      setSubmitError(message);
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    isSubmitting,
    submitError,
    submitSuccess,
    submitResult,
    validationErrors,
    handleSubmit,
    setValidationErrors,
  };
};
