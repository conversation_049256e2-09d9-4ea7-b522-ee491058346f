export interface AccountMethod {
  value: number;
  label: string;
}

export const ACCOUNT_METHODS = {
  PERSONAL_CHECKING: { value: 8, label: "Personal Checking Account" },
  PERSONAL_SAVINGS: { value: 9, label: "Personal Savings Account" },
  BUSINESS_CHECKING: { value: 10, label: "Business Checking Account" },
  BUSINESS_SAVINGS: { value: 11, label: "Business Savings Account" },
} as const;

export const getAccountMethods = (isSoleProprietor: boolean): AccountMethod[] =>
  isSoleProprietor
    ? [
        ACCOUNT_METHODS.PERSONAL_CHECKING,
        ACCOUNT_METHODS.PERSONAL_SAVINGS,
        ACCOUNT_METHODS.BUSINESS_CHECKING,
        ACCOUNT_METHODS.BUSINESS_SAVINGS,
      ]
    : [
        ACCOUNT_METHODS.BUSINESS_CHECKING,
        ACCOUNT_METHODS.BUSINESS_SAVINGS,
        ACCOUNT_METHODS.PERSONAL_CHECKING,
        ACCOUNT_METHODS.PERSONAL_SAVINGS,
      ];

export const VERIFICATION_METHODS = {
  MANUAL: "manual",
  UPLOAD: "upload",
} as const;

export type VerificationMethod = typeof VERIFICATION_METHODS[keyof typeof VERIFICATION_METHODS];

export const BANK_ACCOUNT_VALIDATION = {
  ROUTING_NUMBER_LENGTH: 9,
  ACCOUNT_NUMBER_MIN_LENGTH: 4,
  ACCOUNT_NUMBER_MAX_LENGTH: 17,
  MAX_FILE_SIZE_MB: 10,
  ACCEPTED_FILE_TYPES: "image/*",
} as const;
