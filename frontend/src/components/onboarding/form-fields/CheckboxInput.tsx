interface CheckboxInputProps {
  label: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  error?: string;
  hint?: string;
  className?: string;
  disabled?: boolean;
}

export const CheckboxInput = ({
  label,
  checked,
  onChange,
  error,
  hint,
  className = "",
  disabled = false,
}: CheckboxInputProps) => {
  return (
    <div className={className}>
      <div className={`bg-gray-50 border rounded-lg p-4 ${error ? "border-red-300 bg-red-50" : "border-gray-200"}`}>
        <div className="flex items-start space-x-3">
          <input
            type="checkbox"
            checked={checked}
            onChange={(e) => onChange(e.target.checked)}
            disabled={disabled}
            className={`w-4 h-4 border rounded focus:ring-blue-500 mt-0.5 ${
              error ? "border-red-300 text-red-600 focus:ring-red-500" : "border-gray-300 text-blue-600"
            } ${disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}`}
          />
          <label className={`text-sm text-gray-700 ${disabled ? "opacity-50" : "cursor-pointer"}`}>
            {label}
          </label>
        </div>
        {hint && (
          <p className="text-sm text-gray-600 mt-2 ml-7">{hint}</p>
        )}
        {error && (
          <div className="mt-2 text-sm text-red-600 flex items-center ml-7">
            <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            {error}
          </div>
        )}
      </div>
    </div>
  );
};
