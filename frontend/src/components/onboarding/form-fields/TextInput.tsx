interface TextInputProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  error?: string;
  required?: boolean;
  type?: string;
  maxLength?: number;
  hint?: string;
  className?: string;
  disabled?: boolean;
}

export const TextInput = ({
  label,
  value,
  onChange,
  placeholder,
  error,
  required = false,
  type = "text",
  maxLength,
  hint,
  className = "",
  disabled = false,
}: TextInputProps) => {
  return (
    <div className={className}>
      <label className={`block text-sm font-medium mb-2 ${disabled ? "text-gray-500" : "text-gray-700"}`}>
        {label} {required && "*"}
      </label>
      <input
        type={type}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        className={`w-full px-4 py-3 border rounded-lg transition-colors ${
          disabled
            ? "border-gray-200 bg-gray-100 text-gray-500 cursor-not-allowed"
            : error
            ? "border-red-300 bg-red-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            : "border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        }`}
        placeholder={placeholder}
        maxLength={maxLength}
      />
      {error && (
        <div className="flex items-start mt-1">
          <svg className="w-4 h-4 text-red-600 mr-1 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}
      {hint && !error && <p className="text-gray-500 text-sm mt-1">{hint}</p>}
    </div>
  );
};
