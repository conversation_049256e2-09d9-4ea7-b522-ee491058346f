interface FileUploadProps {
  label: string;
  value: File | null;
  onChange: (file: File | null) => void;
  preview?: string | null;
  onPreviewChange?: (preview: string | null) => void;
  error?: string;
  accept?: string;
  maxSizeMB?: number;
  placeholder?: string;
  hint?: string;
  className?: string;
}

export const FileUpload = ({
  label,
  value,
  onChange,
  preview,
  onPreviewChange,
  error,
  accept = "image/*",
  maxSizeMB = 10,
  placeholder = "Upload a file",
  hint = "PNG, JPG, GIF up to 10MB",
  className = "",
}: FileUploadProps) => {
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > maxSizeMB * 1024 * 1024) {
        return;
      }
      
      onChange(file);
      
      if (onPreviewChange && file.type.startsWith("image/")) {
        const reader = new FileReader();
        reader.onloadend = () => {
          onPreviewChange(reader.result as string);
        };
        reader.readAsDataURL(file);
      }
    }
  };

  const handleRemove = () => {
    onChange(null);
    if (onPreviewChange) {
      onPreviewChange(null);
    }
  };

  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label}
      </label>
      
      <div className={`border-2 border-dashed rounded-lg p-6 text-center hover:border-gray-400 transition-colors ${
        error ? "border-red-300 bg-red-50" : "border-gray-300"
      }`}>
        {!value ? (
          <div>
            <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
              <path
                d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <div className="mt-4">
              <label htmlFor="file-upload" className="cursor-pointer">
                <span className="text-sm font-medium text-blue-600 hover:text-blue-500">
                  {placeholder}
                </span>
                <input
                  id="file-upload"
                  name="file-upload"
                  type="file"
                  className="sr-only"
                  accept={accept}
                  onChange={handleFileChange}
                />
              </label>
              <p className="text-xs text-gray-500 mt-1">or drag and drop</p>
            </div>
            <p className="text-xs text-gray-500 mt-2">{hint}</p>
          </div>
        ) : (
          <div>
            {preview && (
              <img 
                src={preview} 
                alt="Uploaded file" 
                className="mx-auto h-32 w-auto object-contain mb-4" 
              />
            )}
            <p className="text-sm text-gray-900 font-medium">{value.name}</p>
            <p className="text-xs text-gray-500 mt-1">
              {(value.size / 1024 / 1024).toFixed(2)} MB
            </p>
            <button
              type="button"
              onClick={handleRemove}
              className="mt-4 text-sm font-medium text-red-600 hover:text-red-500"
            >
              Remove file
            </button>
          </div>
        )}
      </div>
      
      {error && (
        <div className="flex items-start mt-1">
          <svg className="w-4 h-4 text-red-600 mr-1 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}
    </div>
  );
};
