interface SubmissionErrorSectionProps {
  error: string;
}

export const SubmissionErrorSection = ({ error }: SubmissionErrorSectionProps) => {
  return (
    <div className="mb-6">
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <svg className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <h3 className="text-sm font-medium text-red-900 mb-1">Submission Error</h3>
            <p className="text-sm text-red-800">{typeof error === "string" ? error : "An error occurred"}</p>
          </div>
        </div>
      </div>
    </div>
  );
};
