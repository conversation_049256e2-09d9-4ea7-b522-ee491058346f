import { PAYRIX_LINKS, SUCCESS_NEXT_STEPS, PORTAL_FEATURES } from "../constants/reviewConstants";
import { type SubmitResult } from "../types/reviewTypes";

interface SubmissionSuccessPageProps {
  submitResult: SubmitResult | null;
}

export const SubmissionSuccessPage = ({ submitResult }: SubmissionSuccessPageProps) => {
  const userAccount = submitResult?.data?.userAccount;

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 py-12 px-8">
          <div className="text-center mb-8">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h1 className="text-2xl font-semibold text-gray-900 mb-2">Application Submitted Successfully!</h1>
            <p className="text-gray-600 mb-6">Your merchant onboarding application has been submitted for review.</p>
          </div>

          {userAccount && userAccount.created && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
              <div className="flex items-start space-x-3">
                <svg className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-blue-900 mb-2">Auth Clear Portal Account Created</h3>
                  <div className="text-sm text-blue-800 space-y-1">
                    <p>
                      <strong>Username:</strong> {userAccount.username}
                    </p>
                    <p>
                      <strong>Email:</strong> {userAccount.email}
                    </p>
                    <p className="mt-3">
                      <strong>Portal URL:</strong>{" "}
                      <a
                        href={PAYRIX_LINKS.PORTAL}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 underline"
                      >
                        {PAYRIX_LINKS.PORTAL}
                      </a>
                    </p>
                    <p className="mt-3">You can now log in to your Auth Clear portal using these credentials to:</p>
                    <ul className="list-disc list-inside ml-4 space-y-1">
                      {PORTAL_FEATURES.map((feature, index) => (
                        <li key={index}>{feature}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="bg-gray-50 rounded-lg p-6 mb-6">
            <h3 className="text-sm font-medium text-gray-900 mb-3">What happens next?</h3>
            <div className="text-sm text-gray-700 space-y-2">
              {SUCCESS_NEXT_STEPS.map((step, index) => (
                <p key={index}>• {step}</p>
              ))}
              {userAccount && userAccount.created && (
                <p>• Access your Auth Clear portal using the credentials above</p>
              )}
            </div>
          </div>

          <div className="text-center">
            <button
              onClick={() => (window.location.href = "/")}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              Return to Home
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
