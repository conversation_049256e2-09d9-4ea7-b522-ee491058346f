import { TextInput, SelectInput } from "../form-fields";
import { getAccountMethods } from "../constants/bankAccountConstants";
import { formatDigits, formatRouting } from "../utils/formatting";
import { BankAccountValidationErrors } from "../utils/validation";

interface BankAccountData {
  account: {
    routing?: string;
    number?: string;
    method?: number;
  };
}

interface BankAccountDetailsSectionProps {
  account: BankAccountData;
  errors: BankAccountValidationErrors;
  onChange: (field: string, value: string | number) => void;
  isSoleProprietor: boolean;
}

export const BankAccountDetailsSection = ({
  account,
  errors,
  onChange,
  isSoleProprietor,
}: BankAccountDetailsSectionProps) => {
  const accountMethods = getAccountMethods(isSoleProprietor);

  return (
    <div className="mb-10">
      <h2 className="text-lg font-medium text-gray-900 mb-6">Bank Account Details</h2>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SelectInput
          label="Account Type"
          value={account.account.method || (isSoleProprietor ? 8 : 10)}
          onChange={(value) => onChange("account.method", value)}
          options={accountMethods}
          required
          className="lg:col-span-2"
        />

        <TextInput
          label="Routing Number"
          value={account.account.routing || ""}
          onChange={(value) => onChange("account.routing", formatRouting(value))}
          error={errors.routing}
          required
          placeholder="*********"
          maxLength={9}
          hint="9-digit bank routing number"
        />

        <TextInput
          label="Account Number"
          value={account.account.number || ""}
          onChange={(value) => onChange("account.number", formatDigits(value, 17))}
          error={errors.number}
          required
          placeholder="****************"
          maxLength={17}
          hint="4-17 digit account number"
        />
      </div>
    </div>
  );
};
