import { ACCOUNT_METHODS } from "../constants/reviewConstants";
import { formatAccountNumber } from "../utils/reviewFormatting";
import { type ReviewSectionProps } from "../types/reviewTypes";
import { type CreatePayrixMerchantRequest } from "../../../services/api";

type FormData = Partial<CreatePayrixMerchantRequest>;

interface ReviewBankAccountSectionProps extends ReviewSectionProps {
  formData: FormData;
}

export const ReviewBankAccountSection = ({ formData, onEdit }: ReviewBankAccountSectionProps) => {
  const account = formData.accounts?.[0];

  if (!account) {
    return null;
  }

  return (
    <div className="mb-10">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-medium text-gray-900">Bank Account Information</h2>
        <button onClick={onEdit} className="text-sm text-blue-600 hover:text-blue-700 font-medium flex items-center space-x-1">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
            />
          </svg>
          <span>Edit</span>
        </button>
      </div>
      <div className="bg-gray-50 rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <dt className="text-sm font-medium text-gray-500">Account Type</dt>
            <dd className="text-sm text-gray-900 mt-1">{account.account.method ? ACCOUNT_METHODS[account.account.method] : "—"}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Primary Account</dt>
            <dd className="text-sm text-gray-900 mt-1">{account.primary === 1 ? "Yes" : "No"}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Routing Number</dt>
            <dd className="text-sm text-gray-900 mt-1">{account.account.routing || "—"}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Account Number</dt>
            <dd className="text-sm text-gray-900 mt-1">{formatAccountNumber(account.account.number || "")}</dd>
          </div>
        </div>
      </div>
    </div>
  );
};
