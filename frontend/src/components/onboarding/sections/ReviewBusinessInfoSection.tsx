import { BUSINESS_TYPES } from "../constants/reviewConstants";
import { formatEIN, formatPhone, formatDisplayDate, formatAddress } from "../utils/reviewFormatting";
import { type ReviewSectionProps } from "../types/reviewTypes";
import { type CreatePayrixMerchantRequest } from "../../../services/api";

type FormData = Partial<CreatePayrixMerchantRequest>;

interface ReviewBusinessInfoSectionProps extends ReviewSectionProps {
  formData: FormData;
}

export const ReviewBusinessInfoSection = ({ formData, onEdit }: ReviewBusinessInfoSectionProps) => {
  return (
    <div className="mb-10">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-medium text-gray-900">Business Information</h2>
        <button onClick={onEdit} className="text-sm text-blue-600 hover:text-blue-700 font-medium flex items-center space-x-1">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
            />
          </svg>
          <span>Edit</span>
        </button>
      </div>
      <div className="bg-gray-50 rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <dt className="text-sm font-medium text-gray-500">Legal Business Name</dt>
            <dd className="text-sm text-gray-900 mt-1">{formData.name || "—"}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">DBA</dt>
            <dd className="text-sm text-gray-900 mt-1">{formData.merchant?.dba || "—"}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Business Type</dt>
            <dd className="text-sm text-gray-900 mt-1">{formData.type ? BUSINESS_TYPES[formData.type] : "—"}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Tax ID/EIN</dt>
            <dd className="text-sm text-gray-900 mt-1">{formatEIN(formData.ein || "")}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Email</dt>
            <dd className="text-sm text-gray-900 mt-1">{formData.email || "—"}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Phone</dt>
            <dd className="text-sm text-gray-900 mt-1">{formatPhone(formData.phone || "")}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Website</dt>
            <dd className="text-sm text-gray-900 mt-1">{formData.website || "—"}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">MCC Code</dt>
            <dd className="text-sm text-gray-900 mt-1">{formData.merchant?.mcc || "—"}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Established Date</dt>
            <dd className="text-sm text-gray-900 mt-1">{formatDisplayDate(formData.merchant?.established || "")}</dd>
          </div>
          <div className="md:col-span-2">
            <dt className="text-sm font-medium text-gray-500">Business Address</dt>
            <dd className="text-sm text-gray-900 mt-1">
              {formatAddress(formData.address1, formData.address2, formData.city, formData.state, formData.zip, formData.country)}
            </dd>
          </div>
        </div>
      </div>
    </div>
  );
};
