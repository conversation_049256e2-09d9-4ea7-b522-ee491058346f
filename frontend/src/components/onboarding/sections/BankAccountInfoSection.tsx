export const BankAccountInfoSection = () => {
  return (
    <div className="mb-10">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <div>
            <h3 className="text-sm font-medium text-blue-900 mb-1">Important Information</h3>
            <div className="text-sm text-blue-800 space-y-1">
              <p>• This account will be used for payment processing deposits</p>
              <p>• Ensure the account is active and in good standing</p>
              <p>• The account holder name should match your business name</p>
              <p>• Business accounts are preferred for business transactions</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
