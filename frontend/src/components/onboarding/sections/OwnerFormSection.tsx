import { TextInput, SelectInput } from "../form-fields";
import { Member, OWNERSHIP_CONSTANTS } from "../constants/ownerConstants";
import { US_STATES } from "../constants/businessConstants";
import { formatSSN, formatPhone } from "../utils/formatting";
import { PrimaryContactSection } from "./PrimaryContactSection";

interface OwnerFormSectionProps {
  member: Member;
  index: number;
  canRemove: boolean;
  onFieldChange: (index: number, field: string, value: string | number) => void;
  onRemove: (index: number) => void;
  onPrimaryChange: (index: number) => void;
  errors: Record<string, string>;
}

export const OwnerFormSection = ({ member, index, canRemove, onFieldChange, onRemove, onPrimaryChange, errors }: OwnerFormSectionProps) => {
  const stateOptions = US_STATES.map((state) => ({ value: state, label: state }));

  return (
    <div className="mb-10 border border-gray-200 rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-medium text-gray-900">
          Principal {index + 1} Information
          {member.ownership > 0 && (
            <span className="text-sm text-gray-600 ml-2">({(member.ownership / OWNERSHIP_CONSTANTS.PERCENTAGE_DIVISOR).toFixed(2)}% ownership)</span>
          )}
        </h2>
        {canRemove && (
          <button type="button" onClick={() => onRemove(index)} className="text-red-600 hover:text-red-800 text-sm font-medium">
            Remove Principal
          </button>
        )}
      </div>

      <PrimaryContactSection member={member} index={index} onPrimaryChange={onPrimaryChange} error={errors.primaryContact} />

      {/* Personal Information */}
      <div className="mb-8">
        <h3 className="text-md font-medium text-gray-900 mb-4">Personal Information</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <TextInput
            label="First Name"
            value={member.first || ""}
            onChange={(value) => onFieldChange(index, "first", value)}
            placeholder="John"
            error={errors[`member${index}.first`]}
            required
          />

          <TextInput
            label="Middle Name"
            value={member.middle || ""}
            onChange={(value) => onFieldChange(index, "middle", value)}
            placeholder="Optional"
          />

          <TextInput
            label="Last Name"
            value={member.last || ""}
            onChange={(value) => onFieldChange(index, "last", value)}
            placeholder="Smith"
            error={errors[`member${index}.last`]}
            required
          />

          <TextInput
            label="Business Title"
            value={member.title || ""}
            onChange={(value) => onFieldChange(index, "title", value)}
            placeholder="CEO, Owner, President, etc."
            error={errors[`member${index}.title`]}
            required
          />

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Date of Birth *</label>
            <input
              type="date"
              value={
                member.dob
                  ? member.dob.length === 8
                    ? `${member.dob.slice(0, 4)}-${member.dob.slice(4, 6)}-${member.dob.slice(6, 8)}`
                    : member.dob
                  : ""
              }
              onChange={(e) => onFieldChange(index, "dob", e.target.value.replace(/-/g, ""))}
              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                errors[`member${index}.dob`] ? "border-red-300 bg-red-50" : "border-gray-300"
              }`}
            />
            {errors[`member${index}.dob`] && <p className="text-red-600 text-sm mt-1">{errors[`member${index}.dob`]}</p>}
            <p className="text-gray-500 text-sm mt-1">Must be at least 18 years old</p>
          </div>

          <TextInput
            label="Social Security Number"
            value={formatSSN(member.ssn || "")}
            onChange={(value) => onFieldChange(index, "ssn", value.replace(/\D/g, ""))}
            placeholder="***********"
            maxLength={11}
            error={errors[`member${index}.ssn`]}
            required
          />

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Ownership Percentage *</label>
            <div className="relative">
              <input
                type="number"
                min="0"
                max="100"
                value={Math.round((member.ownership || 0) / OWNERSHIP_CONSTANTS.PERCENTAGE_DIVISOR)}
                onChange={(e) => onFieldChange(index, "ownership", parseInt(e.target.value) * OWNERSHIP_CONSTANTS.PERCENTAGE_DIVISOR || 0)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors pr-8 ${
                  errors[`member${index}.ownership`] ? "border-red-300 bg-red-50" : "border-gray-300"
                }`}
                placeholder="25"
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                <span className="text-gray-500">%</span>
              </div>
            </div>
            {member.ownership < OWNERSHIP_CONSTANTS.MINIMUM_REQUIRED_OWNERSHIP && member.ownership > 0 && (
              <p className="text-amber-600 text-sm mt-1">Only principals with 25% or more ownership are required</p>
            )}
          </div>
        </div>
      </div>

      {/* Contact Information */}
      <div className="mb-8">
        <h3 className="text-md font-medium text-gray-900 mb-4">Contact Information</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <TextInput
            label="Personal Email"
            value={member.email || ""}
            onChange={(value) => onFieldChange(index, "email", value)}
            placeholder="<EMAIL>"
            type="email"
            error={errors[`member${index}.email`]}
            required
          />

          <TextInput
            label="Personal Phone"
            value={formatPhone(member.phone || "")}
            onChange={(value) => onFieldChange(index, "phone", value.replace(/\D/g, ""))}
            placeholder="(*************"
            type="tel"
            maxLength={14}
            error={errors[`member${index}.phone`]}
            required
          />
        </div>
      </div>

      {/* Personal Address */}
      <div className="mb-8">
        <h3 className="text-md font-medium text-gray-900 mb-4">Personal Address</h3>
        <p className="text-gray-600 mb-4">Enter home address (not business address)</p>
        <p className="text-sm text-red-600 mb-4">* PO Boxes are not acceptable for onboarding</p>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="lg:col-span-2">
            <TextInput
              label="Street Address"
              value={member.address1 || ""}
              onChange={(value) => onFieldChange(index, "address1", value)}
              placeholder="123 Home Street"
              error={errors[`member${index}.address1`]}
              required
            />
          </div>

          <div className="lg:col-span-2">
            <TextInput
              label="Address Line 2"
              value={member.address2 || ""}
              onChange={(value) => onFieldChange(index, "address2", value)}
              placeholder="Apt, suite, etc. (optional)"
            />
          </div>

          <TextInput
            label="City"
            value={member.city || ""}
            onChange={(value) => onFieldChange(index, "city", value)}
            placeholder="Los Angeles"
            error={errors[`member${index}.city`]}
            required
          />

          <SelectInput
            label="State"
            value={member.state || ""}
            onChange={(value) => onFieldChange(index, "state", value as string)}
            options={stateOptions}
            placeholder="Select state"
            error={errors[`member${index}.state`]}
            required
          />

          <TextInput
            label="ZIP Code"
            value={member.zip || ""}
            onChange={(value) => onFieldChange(index, "zip", value)}
            placeholder="90210"
            error={errors[`member${index}.zip`]}
            required
          />

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Country</label>
            <input type="text" value="USA" readOnly className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50" />
          </div>
        </div>
      </div>

      {/* Driver's License */}
      <div className="mb-8">
        <h3 className="text-md font-medium text-gray-900 mb-4">Driver's License Information</h3>
        <p className="text-gray-600 mb-4">Required for identity verification and KYC compliance</p>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <TextInput
            label="Driver's License Number"
            value={member.dl || ""}
            onChange={(value) => onFieldChange(index, "dl", value)}
            placeholder="********"
            error={errors[`member${index}.dl`]}
            required
          />

          <SelectInput
            label="Issuing State"
            value={member.dlstate || ""}
            onChange={(value) => onFieldChange(index, "dlstate", value as string)}
            options={stateOptions}
            placeholder="Select state"
            error={errors[`member${index}.dlstate`]}
            required
          />
        </div>
      </div>

      {/* Responsibilities & Status */}
      <div>
        <h3 className="text-md font-medium text-gray-900 mb-4">Responsibilities & Status</h3>
        <div className="space-y-4">
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={member.significantResponsibility === 1}
                onChange={(e) => onFieldChange(index, "significantResponsibility", e.target.checked ? 1 : 0)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <label className="text-sm font-medium text-gray-700">This person has significant responsibility for the business</label>
            </div>
            <p className="text-gray-600 text-sm mt-2 ml-7">
              Check this if the person is a CEO, CFO, Owner, VP, managing member, or similar controlling authority.
            </p>
          </div>

          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={member.politicallyExposed === 1}
                onChange={(e) => onFieldChange(index, "politicallyExposed", e.target.checked ? 1 : 0)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <label className="text-sm font-medium text-gray-700">This person is politically exposed</label>
            </div>
            <p className="text-gray-600 text-sm mt-2 ml-7">
              A politically exposed person is someone who, through their prominent position or influence, is more susceptible to being involved in
              bribery or corruption.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
