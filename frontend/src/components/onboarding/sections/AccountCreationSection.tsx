import { TextInput } from "../form-fields";

interface AccountCreationSectionProps {
  username: string;
  password: string;
  confirmPassword: string;
  onFieldChange: (field: string, value: string) => void;
  errors: {
    username?: string;
    password?: string;
    confirmPassword?: string;
  };
}

export const AccountCreationSection = ({ username, password, confirmPassword, onFieldChange, errors }: AccountCreationSectionProps) => {
  return (
    <div className="mb-10">
      <h2 className="text-lg font-medium text-gray-900 mb-6">Auth Clear Portal Account</h2>
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
        <div className="flex items-start space-x-3">
          <svg className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <p className="text-sm font-medium text-blue-900 mb-1">Required Portal Account</p>
            <p className="text-blue-800 text-sm">
              An Auth Clear portal account is required for all merchants. This account provides access to your dashboard for viewing transactions,
              managing settings, and accessing reports.
            </p>
          </div>
        </div>
      </div>
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <TextInput
            label="Username"
            value={username}
            onChange={(value) => onFieldChange("username", value.toLowerCase())}
            placeholder="merchant123"
            error={errors.username}
            required
            hint={!errors.username ? "3-50 characters, lowercase letters and at least one number only" : undefined}
          />

          <TextInput
            label="Password"
            value={password}
            onChange={(value) => onFieldChange("password", value)}
            placeholder="Create a strong password"
            type="password"
            error={errors.password}
            required
            hint={!errors.password ? "Min 8 chars: uppercase, lowercase, number, special character (@$!%*?&)" : undefined}
          />

          <div className="lg:col-span-2">
            <TextInput
              label="Confirm Password"
              value={confirmPassword}
              onChange={(value) => onFieldChange("confirmPassword", value)}
              placeholder="Confirm your password"
              type="password"
              error={errors.confirmPassword}
              required
            />
          </div>
        </div>

        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <svg className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div className="text-sm text-amber-800">
              <p className="font-medium mb-1">Account Creation Notice</p>
              <p>
                The account will be created after successful merchant onboarding. You will receive login credentials and a link to access your Auth
                Clear portal dashboard.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
