import { BankAccountValidationErrors } from "../utils/validation";

interface BankAccountTermsSectionProps {
  termsAccepted: boolean;
  onTermsChange: (accepted: boolean) => void;
  errors: BankAccountValidationErrors;
}

export const BankAccountTermsSection = ({
  termsAccepted,
  onTermsChange,
  errors,
}: BankAccountTermsSectionProps) => {
  return (
    <div className="mb-10">
      <div className={`bg-gray-50 border rounded-lg p-6 ${errors.terms ? "border-red-300 bg-red-50" : "border-gray-200"}`}>
        <div className="flex items-start space-x-3">
          <input
            type="checkbox"
            id="terms"
            checked={termsAccepted}
            onChange={(e) => onTermsChange(e.target.checked)}
            className={`w-4 h-4 border rounded focus:ring-blue-500 mt-0.5 ${
              errors.terms ? "border-red-300 text-red-600 focus:ring-red-500" : "border-gray-300 text-blue-600"
            }`}
          />
          <label htmlFor="terms" className="text-sm text-gray-700">
            <span className="font-medium">I acknowledge and agree</span> that the bank account information provided is accurate and that I
            authorize the use of this account for payment processing. I understand that incorrect information may result in processing delays
            or account restrictions.
          </label>
        </div>
        {errors.terms && (
          <div className="flex items-start mt-3 ml-7">
            <svg className="w-4 h-4 text-red-600 mr-1 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-sm text-red-600">{errors.terms}</p>
          </div>
        )}
      </div>
    </div>
  );
};
