export const FinCenNotice = () => {
  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
      <h3 className="text-sm font-medium text-blue-900 mb-2">Important Notice - FinCEN Requirements</h3>
      <p className="text-sm text-blue-800">
        To help the government fight the funding of terrorism and money laundering activities, federal law requires all financial institutions to
        obtain, verify, and record information that identifies each individual or business who opens an account.
      </p>
      <p className="text-sm text-blue-800 mt-2">
        <strong>What this means for you:</strong> When you open an account, we will ask for your identifiable information including your full name,
        address, date of birth, and other business information that will allow us to identify you. We may also ask to see your Identification Card,
        Driver&apos;s License, and/or other identifying documents.
      </p>
      <p className="text-sm text-blue-800 mt-2">
        <strong>Please Note:</strong> The following information must be provided for Sole Proprietors or each individual, if any, who directly or
        indirectly owns twenty-five percent (25%) or more of the ownership interest of the Legal Entity in this application as well as an individual
        with significant responsibility. A Legal Entity includes a general partnership, a corporation, limited liability company or other entity that
        is formed by a filing of a public document with a Secretary of State or similar office, and any similar business entity formed in the United
        States.
      </p>
    </div>
  );
};
