import { formatSSN, formatPhone, formatDisplayDate, formatAddress, formatOwnershipPercentage, formatDriversLicense } from "../utils/reviewFormatting";
import { type ReviewSectionProps } from "../types/reviewTypes";
import { type CreatePayrixMerchantRequest } from "../../../services/api";

type FormData = Partial<CreatePayrixMerchantRequest>;

interface ReviewOwnerInfoSectionProps extends ReviewSectionProps {
  formData: FormData;
}

export const ReviewOwnerInfoSection = ({ formData, onEdit }: ReviewOwnerInfoSectionProps) => {
  const member = formData.merchant?.members?.[0];

  if (!member) {
    return null;
  }

  return (
    <div className="mb-10">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-medium text-gray-900">Owner Information</h2>
        <button onClick={onEdit} className="text-sm text-blue-600 hover:text-blue-700 font-medium flex items-center space-x-1">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
            />
          </svg>
          <span>Edit</span>
        </button>
      </div>
      <div className="bg-gray-50 rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <dt className="text-sm font-medium text-gray-500">Full Name</dt>
            <dd className="text-sm text-gray-900 mt-1">
              {`${member.first || ""} ${member.middle ? member.middle + " " : ""}${member.last || ""}`.trim() || "—"}
            </dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Title</dt>
            <dd className="text-sm text-gray-900 mt-1">{member.title || "—"}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Date of Birth</dt>
            <dd className="text-sm text-gray-900 mt-1">{formatDisplayDate(member.dob || "")}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">SSN</dt>
            <dd className="text-sm text-gray-900 mt-1">{formatSSN(member.ssn || "")}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Email</dt>
            <dd className="text-sm text-gray-900 mt-1">{member.email || "—"}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Phone</dt>
            <dd className="text-sm text-gray-900 mt-1">{formatPhone(member.phone || "")}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Ownership Percentage</dt>
            <dd className="text-sm text-gray-900 mt-1">{formatOwnershipPercentage(member.ownership || 0)}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Driver&apos;s License</dt>
            <dd className="text-sm text-gray-900 mt-1">{formatDriversLicense(member.dl || "", member.dlstate || "")}</dd>
          </div>
          <div className="md:col-span-2">
            <dt className="text-sm font-medium text-gray-500">Personal Address</dt>
            <dd className="text-sm text-gray-900 mt-1">
              {formatAddress(member.address1, member.address2, member.city, member.state, member.zip, member.country)}
            </dd>
          </div>
          <div className="md:col-span-2">
            <dt className="text-sm font-medium text-gray-500">Responsibilities & Status</dt>
            <dd className="text-sm text-gray-900 mt-1">
              <div className="flex flex-wrap gap-2">
                {member.significantResponsibility === 1 && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Significant Responsibility
                  </span>
                )}
                {member.politicallyExposed === 1 && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Politically Exposed
                  </span>
                )}
                {member.significantResponsibility !== 1 && member.politicallyExposed !== 1 && <span className="text-gray-500">None</span>}
              </div>
            </dd>
          </div>
        </div>
      </div>
    </div>
  );
};
