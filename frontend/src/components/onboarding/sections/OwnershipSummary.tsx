import { Member, OWNERSHIP_CONSTANTS } from "../constants/ownerConstants";

interface OwnershipSummaryProps {
  members: Member[];
}

export const OwnershipSummary = ({ members }: OwnershipSummaryProps) => {
  if (members.length <= 1) return null;

  const totalOwnership = members.reduce((sum, m) => sum + (m.ownership || 0), 0);
  const isValid = totalOwnership === OWNERSHIP_CONSTANTS.FULL_OWNERSHIP;

  return (
    <div
      className={`mb-6 p-4 rounded-lg ${
        isValid ? "bg-green-50 border border-green-200" : "bg-red-50 border border-red-200"
      }`}
    >
      <div className="flex items-center justify-between">
        <span className={`text-sm font-medium ${isValid ? "text-green-800" : "text-red-800"}`}>
          Total Ownership: {(totalOwnership / OWNERSHIP_CONSTANTS.PERCENTAGE_DIVISOR).toFixed(2)}%
        </span>
        {!isValid && <span className="text-sm text-red-600">Must equal 100%</span>}
      </div>
    </div>
  );
};
