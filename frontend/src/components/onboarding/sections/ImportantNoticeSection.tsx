import { IMPORTANT_NOTICES } from "../constants/reviewConstants";

export const ImportantNoticeSection = () => {
  return (
    <div className="mb-10">
      <div className="bg-amber-50 border border-amber-200 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-amber-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <div>
            <h3 className="text-sm font-medium text-amber-900 mb-1">Important Notice</h3>
            <div className="text-sm text-amber-800 space-y-1">
              {IMPORTANT_NOTICES.map((notice, index) => (
                <p key={index}>• {notice}</p>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
