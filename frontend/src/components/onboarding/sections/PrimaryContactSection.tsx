import { Member } from "../constants/ownerConstants";

interface PrimaryContactSectionProps {
  member: Member;
  index: number;
  onPrimaryChange: (index: number) => void;
  error?: string;
}

export const PrimaryContactSection = ({ member, index, onPrimaryChange, error }: PrimaryContactSectionProps) => {
  return (
    <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
      <h3 className="text-sm font-semibold text-blue-900 mb-2">Primary Contact Designation</h3>
      <label className="flex items-center space-x-3 cursor-pointer">
        <input
          type="radio"
          name="primaryContact"
          checked={member.primary === "1"}
          onChange={() => onPrimaryChange(index)}
          className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
        />
        <span className="text-sm font-medium text-gray-700">
          Select as primary contact for the business
          {member.primary === "1" && <span className="text-blue-600 ml-2">(Currently Selected)</span>}
        </span>
      </label>
      <p className="text-xs text-gray-600 mt-2">
        The primary contact will receive all business communications and account notifications.
      </p>
      {error && index === 0 && <p className="text-red-600 text-sm mt-1">{error}</p>}
    </div>
  );
};
