import { TextInput, SelectInput, DateInput, CheckboxInput } from "../form-fields";
import { BUSINESS_TYPES, BUSINESS_TYPE_CHECKS } from "../constants/businessConstants";
import { MCC_CODES } from "../constants/mccCodes";
import { formatEIN, formatDateToISO, formatISOToDate } from "../utils/formatting";
import { ValidationErrors, FormData } from "../utils/validation";

interface BusinessDetailsSectionProps {
  formData: FormData;
  errors: ValidationErrors;
  onChange: (field: string, value: string | number) => void;
}

export const BusinessDetailsSection = ({ formData, errors, onChange }: BusinessDetailsSectionProps) => {
  const { isSoleProprietor, requiresCorporateStructure } = BUSINESS_TYPE_CHECKS;
  const selectedBusinessType = formData.type;
  const isSole = selectedBusinessType && isSoleProprietor(selectedBusinessType);
  const requiresStructure = selectedBusinessType && requiresCorporateStructure(selectedBusinessType);

  return (
    <div className="mb-10">
      <h2 className="text-lg font-medium text-gray-900 mb-6">Business Details</h2>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <TextInput
          label="Legal Business Name"
          value={formData.name || ""}
          onChange={(value) => onChange("name", value)}
          error={errors.name}
          required
          placeholder="Enter your legal business name"
          className="lg:col-span-2"
        />

        <TextInput
          label={`DBA / Statement Descriptor${requiresStructure ? "" : ""}`}
          value={formData.merchant?.dba || ""}
          onChange={(value) => onChange("merchant.dba", value)}
          error={errors.dba}
          required={!!requiresStructure}
          placeholder={isSole ? "Optional - how your business appears on statements" : "How your business appears on statements"}
          hint={
            isSole
              ? "Optional - defaults to legal name if not provided"
              : requiresStructure
              ? "Required - how your business appears on customer statements"
              : undefined
          }
        />

        <SelectInput
          label="Business Type"
          value={formData.type || ""}
          onChange={(value) => onChange("type", value)}
          options={BUSINESS_TYPES}
          error={errors.type}
          required
          placeholder="Select business type"
        />

        <TextInput
          label={isSole ? "Tax ID / EIN or SSN" : "Tax ID / EIN"}
          value={formatEIN(formData.ein || "")}
          onChange={(value) => onChange("ein", value.replace(/\D/g, ""))}
          error={errors.ein}
          required
          placeholder={isSole ? "12-3456789 or ***********" : "12-3456789"}
          maxLength={isSole ? 11 : 10}
          hint={isSole ? "9-digit EIN or 9-digit SSN (sole proprietors may use either)" : "9-digit EIN required for business entities"}
        />

        <TextInput
          label="Website"
          value={formData.website || ""}
          onChange={(value) => onChange("website", value)}
          error={errors.website}
          required
          type="url"
          placeholder="https://www.yourbusiness.com"
          hint="Use https://nowebsite.com if you don't have one"
        />

        <div>
          <SelectInput
            label="Industry (MCC - Merchant Category Code)"
            value={formData.merchant?.mcc || ""}
            onChange={(value) => onChange("merchant.mcc", value)}
            options={MCC_CODES.map((mcc) => ({
              value: mcc.code,
              label: `${mcc.code} - ${mcc.description}`,
            }))}
            error={errors.mcc}
            required
            placeholder="Select your industry"
          />
          <p className="text-sm text-[#697282] mt-1">If your business type is not listed, please contact support.</p>
        </div>

        <CheckboxInput
          label="Is this your first time accepting digital payments for your business?"
          checked={formData.merchant?.new === 1}
          onChange={(checked) => onChange("merchant.new", checked ? 1 : 0)}
          hint="Check this if you have never processed digital payments before. This will help us tailor the onboarding process for your business."
          className="lg:col-span-2"
        />

        <TextInput
          label="Annual Sales"
          value={String(formData.merchant?.annualCCSales || "")}
          onChange={(value) => onChange("merchant.annualCCSales", value ? parseInt(value) : 0)}
          error={errors.annualCCSales}
          required={formData.merchant?.new !== 1}
          type="number"
          placeholder="100000"
          disabled={formData.merchant?.new === 1}
          hint={formData.merchant?.new === 1 ? "Set to $0 for first-time digital payment processors" : "Expected annual sales volume in USD"}
        />

        <TextInput
          label="Average Transaction Amount"
          value={String(formData.merchant?.avgTicket || "")}
          onChange={(value) => onChange("merchant.avgTicket", value ? parseInt(value) : 0)}
          error={errors.avgTicket}
          type="number"
          placeholder="50"
          hint="Average amount per transaction in USD"
        />

        <DateInput
          label="Business Established Date"
          value={formData.merchant?.established ? formatDateToISO(formData.merchant.established) : ""}
          onChange={(value) => onChange("merchant.established", formatISOToDate(value))}
          error={errors.established}
          required
          max={new Date().toISOString().split("T")[0]}
          hint="When was your business legally established?"
        />
      </div>
    </div>
  );
};
