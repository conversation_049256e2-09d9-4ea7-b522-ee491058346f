import { useDispatch, useSelector } from "react-redux";
import { type RootState } from "../../redux/store.ts";
import { prevStep, goToStep } from "../../redux/slices/onboardingSlice.ts";
import { useState } from "react";
import {
  ReviewBusinessInfoSection,
  ReviewOwnerInfoSection,
  ReviewBankAccountSection,
  ComplianceCheckboxesSection,
  ImportantNoticeSection,
  SubmissionErrorSection,
  SubmissionSuccessPage,
  SubmissionLoadingOverlay,
} from "./sections";
import { useClientIp } from "./hooks/useClientIp";
import { useSubmitOnboarding } from "./hooks/useSubmitOnboarding";
import { type ComplianceState, type ComplianceValidationErrors } from "./utils/reviewValidation";

const OnboardingReview = () => {
  const dispatch = useDispatch();
  const { formData } = useSelector((state: RootState) => state.onboarding);
  const clientIp = useClientIp();

  const [complianceState, setComplianceState] = useState<ComplianceState>({
    visaDisclosure: false,
    payrixTerms: false,
    attestationStatement: false,
    tcAttestation: false, // Syncs with attestationStatement
  });

  const { isSubmitting, submitError, submitSuccess, submitResult, validationErrors, handleSubmit, setValidationErrors } = useSubmitOnboarding({
    formData,
    complianceState,
    clientIp,
  });

  const handleComplianceChange = (field: keyof ComplianceState, value: boolean) => {
    setComplianceState((prev) => ({
      ...prev,
      [field]: value,
      // Sync tcAttestation with attestationStatement
      ...(field === "attestationStatement" ? { tcAttestation: value } : {}),
    }));

    // Clear validation error when user checks the box
    if (value && validationErrors[field]) {
      setValidationErrors((prev: ComplianceValidationErrors) => ({ ...prev, [field]: undefined }));
    }
  };

  if (submitSuccess) {
    return <SubmissionSuccessPage submitResult={submitResult} />;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {/* Header */}
          <div className="border-b border-gray-200 px-8 py-6">
            <h1 className="text-2xl font-semibold text-gray-900">Review & Submit</h1>
            <p className="text-gray-600 mt-1">Please review all information before submitting your application</p>
          </div>

          <div className="px-8 py-8">
            <ReviewBusinessInfoSection formData={formData} onEdit={() => dispatch(goToStep(1))} />

            <ReviewOwnerInfoSection formData={formData} onEdit={() => dispatch(goToStep(2))} />

            <ReviewBankAccountSection formData={formData} onEdit={() => dispatch(goToStep(3))} />

            <ComplianceCheckboxesSection complianceState={complianceState} validationErrors={validationErrors} onChange={handleComplianceChange} />

            <ImportantNoticeSection />

            {submitError && <SubmissionErrorSection error={submitError} />}

            {/* Navigation Buttons */}
            <div className="border-t border-gray-200 pt-6 flex justify-between">
              <button
                type="button"
                onClick={() => dispatch(prevStep())}
                disabled={isSubmitting}
                className="bg-gray-100 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-200 focus:ring-4 focus:ring-gray-200 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {isSubmitting && (
                  <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    />
                  </svg>
                )}
                <span>{isSubmitting ? "Submitting..." : "Submit Application"}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {isSubmitting && <SubmissionLoadingOverlay />}
    </div>
  );
};

export default OnboardingReview;
