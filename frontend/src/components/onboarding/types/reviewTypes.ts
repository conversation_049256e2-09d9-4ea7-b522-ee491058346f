export interface SubmitResult {
  data: {
    userAccount: {
      created: boolean;
      username: string;
      email: string;
    };
  };
}

export interface ReviewSectionProps {
  onEdit: () => void;
}

export interface ComplianceCheckboxProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  error?: string;
  label: string;
  links?: Array<{
    text: string;
    url: string;
  }>;
}

export interface SubmissionState {
  isSubmitting: boolean;
  submitError: string | null;
  submitSuccess: boolean;
  submitResult: SubmitResult | null;
}
