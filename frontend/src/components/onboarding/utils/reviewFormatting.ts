export const formatSSN = (ssn: string): string => {
  if (!ssn) return "";
  const digits = ssn.replace(/\D/g, "");
  return `***-**-${digits.slice(-4)}`;
};

export const formatAccountNumber = (accountNumber: string): string => {
  if (!accountNumber) return "";
  const digits = accountNumber.replace(/\D/g, "");
  return `****${digits.slice(-4)}`;
};

export const formatPhone = (phone: string): string => {
  if (!phone) return "";
  const digits = phone.replace(/\D/g, "");
  if (digits.length === 10) {
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
  }
  return phone;
};

export const formatEIN = (ein: string): string => {
  if (!ein) return "";
  const digits = ein.replace(/\D/g, "");
  if (digits.length === 9) {
    return `${digits.slice(0, 2)}-${digits.slice(2)}`;
  }
  return ein;
};

export const formatDisplayDate = (dateStr: string): string => {
  if (!dateStr) return "—";
  
  if (dateStr.length === 8) {
    // YYYYMMDD format
    return `${dateStr.slice(4, 6)}/${dateStr.slice(6, 8)}/${dateStr.slice(0, 4)}`;
  }
  
  return dateStr;
};

export const formatOwnershipPercentage = (ownership: number): string => {
  return `${Math.round((ownership || 0) / 100)}%`;
};

export const formatDriversLicense = (dl: string, dlstate: string): string => {
  if (!dl) return "Not provided";
  return `${dl} (${dlstate || ""})`;
};

export const formatAddress = (
  address1?: string,
  address2?: string,
  city?: string,
  state?: string,
  zip?: string,
  country?: string
): string => {
  const parts = [
    address1,
    address2,
    city && state && zip ? `${city}, ${state} ${zip}` : "",
    country
  ].filter(Boolean);
  
  return parts.join(", ") || "—";
};
