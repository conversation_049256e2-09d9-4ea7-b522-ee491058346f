import { Member, OWNERSHIP_CONSTANTS, VALIDATION_PATTERNS, MINIMUM_AGE } from "../constants/ownerConstants";

export interface OwnerValidationErrors {
  [key: string]: string;
}

export const validateMemberRequiredFields = (member: Member, index: number): OwnerValidationErrors => {
  const errors: OwnerValidationErrors = {};
  const prefix = `member${index}`;

  if (!member.first?.trim()) errors[`${prefix}.first`] = "First name is required";
  if (!member.last?.trim()) errors[`${prefix}.last`] = "Last name is required";
  if (!member.title?.trim()) errors[`${prefix}.title`] = "Business title is required";
  if (!member.email?.trim()) errors[`${prefix}.email`] = "Email is required";
  if (!member.phone?.trim()) errors[`${prefix}.phone`] = "Phone is required";
  if (!member.dob?.trim()) errors[`${prefix}.dob`] = "Date of birth is required";
  if (!member.ssn?.trim()) errors[`${prefix}.ssn`] = "SSN is required";
  if (!member.dl?.trim()) errors[`${prefix}.dl`] = "Driver's license number is required";
  if (!member.dlstate?.trim()) errors[`${prefix}.dlstate`] = "Driver's license state is required";

  if (!member.address1?.trim()) {
    errors[`${prefix}.address1`] = "Address is required";
  } else if (VALIDATION_PATTERNS.PO_BOX.test(member.address1)) {
    errors[`${prefix}.address1`] = "PO Boxes are not acceptable for personal address";
  }

  if (!member.city?.trim()) errors[`${prefix}.city`] = "City is required";
  if (!member.state?.trim()) errors[`${prefix}.state`] = "State is required";
  if (!member.zip?.trim()) errors[`${prefix}.zip`] = "ZIP code is required";

  return errors;
};

export const validateMemberFormats = (member: Member, index: number): OwnerValidationErrors => {
  const errors: OwnerValidationErrors = {};
  const prefix = `member${index}`;

  if (member.email && !VALIDATION_PATTERNS.EMAIL.test(member.email)) {
    errors[`${prefix}.email`] = "Please enter a valid email address";
  }

  if (member.phone && !VALIDATION_PATTERNS.PHONE.test(member.phone.replace(/\D/g, ""))) {
    errors[`${prefix}.phone`] = "Phone must be 10 digits";
  }

  if (member.ssn && !VALIDATION_PATTERNS.SSN.test(member.ssn.replace(/\D/g, ""))) {
    errors[`${prefix}.ssn`] = "SSN must be 9 digits";
  }

  if (member.zip && !VALIDATION_PATTERNS.ZIP.test(member.zip)) {
    errors[`${prefix}.zip`] = "ZIP code must be 5 digits or 5-4 format";
  }

  if (member.dlstate && !VALIDATION_PATTERNS.STATE.test(member.dlstate)) {
    errors[`${prefix}.dlstate`] = "Driver's license state must be a valid 2-letter state code";
  }

  return errors;
};

export const validateMemberAge = (member: Member, index: number): OwnerValidationErrors => {
  const errors: OwnerValidationErrors = {};
  const prefix = `member${index}`;

  if (member.dob) {
    const dobDate = new Date(member.dob);
    const today = new Date();
    const age = today.getFullYear() - dobDate.getFullYear();
    const monthDiff = today.getMonth() - dobDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dobDate.getDate())) {
      if (age - 1 < MINIMUM_AGE) {
        errors[`${prefix}.dob`] = `Owner must be at least ${MINIMUM_AGE} years old`;
      }
    } else if (age < MINIMUM_AGE) {
      errors[`${prefix}.dob`] = `Owner must be at least ${MINIMUM_AGE} years old`;
    }
  }

  return errors;
};

export const validateMember = (member: Member, index: number, members: Member[]): OwnerValidationErrors => {
  const shouldValidate = member.ownership >= OWNERSHIP_CONSTANTS.MINIMUM_REQUIRED_OWNERSHIP || members.length === 1;
  
  if (!shouldValidate) {
    return {};
  }

  return {
    ...validateMemberRequiredFields(member, index),
    ...validateMemberFormats(member, index),
    ...validateMemberAge(member, index),
  };
};

export const validateTotalOwnership = (members: Member[]): OwnerValidationErrors => {
  const errors: OwnerValidationErrors = {};
  const totalOwnership = members.reduce((sum, m) => sum + (m.ownership || 0), 0);

  if (totalOwnership !== OWNERSHIP_CONSTANTS.FULL_OWNERSHIP) {
    errors.totalOwnership = `Total ownership must equal 100% (currently ${(totalOwnership / OWNERSHIP_CONSTANTS.PERCENTAGE_DIVISOR).toFixed(2)}%)`;
  }

  return errors;
};

export const validatePrimaryContact = (members: Member[]): OwnerValidationErrors => {
  const errors: OwnerValidationErrors = {};
  const primaryCount = members.filter((m) => m.primary === "1").length;

  if (primaryCount === 0) {
    errors.primaryContact = "You must designate one owner as the primary contact";
  } else if (primaryCount > 1) {
    errors.primaryContact = "Only one owner can be designated as the primary contact";
  }

  return errors;
};

export const validateAccountCredentials = (username?: string, password?: string, confirmPassword?: string): OwnerValidationErrors => {
  const errors: OwnerValidationErrors = {};

  if (!username?.trim()) {
    errors.username = "Username is required";
  } else {
    if (!/^[a-z0-9]{3,50}$/.test(username)) {
      errors.username = "Username must be 3-50 characters and contain only lowercase letters and numbers";
    } else if (!/(?=.*\d)/.test(username)) {
      errors.username = "Username must include at least one number";
    }
  }

  if (!password?.trim()) {
    errors.password = "Password is required";
  } else {
    const passwordErrors = [];
    if (password.length < 8) passwordErrors.push("at least 8 characters");
    if (!/(?=.*[a-z])/.test(password)) passwordErrors.push("one lowercase letter");
    if (!/(?=.*[A-Z])/.test(password)) passwordErrors.push("one uppercase letter");
    if (!/(?=.*\d)/.test(password)) passwordErrors.push("one number");
    if (!/(?=.*[@$!%*?&])/.test(password)) passwordErrors.push("one special character (@$!%*?&)");

    if (passwordErrors.length > 0) {
      errors.password = `Password must include: ${passwordErrors.join(", ")}`;
    }
  }

  if (password && password !== confirmPassword) {
    errors.confirmPassword = "Passwords do not match";
  }

  return errors;
};

export const validateOwnerForm = (
  members: Member[],
  username?: string,
  password?: string,
  confirmPassword?: string
): OwnerValidationErrors => {
  let errors: OwnerValidationErrors = {};

  errors = { ...errors, ...validateTotalOwnership(members) };
  errors = { ...errors, ...validatePrimaryContact(members) };
  errors = { ...errors, ...validateAccountCredentials(username, password, confirmPassword) };

  members.forEach((member, index) => {
    const memberErrors = validateMember(member, index, members);
    errors = { ...errors, ...memberErrors };
  });

  return errors;
};
