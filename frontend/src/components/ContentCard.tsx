import { type ReactNode } from "react";

interface ContentCardProps {
  id?: string;
  title?: string;
  subtitle?: string;
  children: ReactNode;
  className?: string;
  variant?: string;
}

export const ContentCard = ({ 
  id, 
  title, 
  subtitle, 
  children, 
  className = "" 
}: ContentCardProps) => {
  return (
    <div id={id} className={`bg-white rounded-xl shadow-sm border border-slate-200 p-6 ${className}`}>
      {title && (
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-slate-800">{title}</h2>
          {subtitle && <p className="text-sm text-slate-600 mt-1">{subtitle}</p>}
        </div>
      )}
      <div className="prose prose-slate max-w-none">
        {children}
      </div>
    </div>
  );
};