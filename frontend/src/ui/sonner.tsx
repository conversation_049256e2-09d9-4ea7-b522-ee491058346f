import * as React from "react";
import { Toaster as Sonner } from "sonner";

type ToasterProps = React.ComponentProps<typeof Sonner>;

const Toaster = ({ ...props }: ToasterProps) => {
  return (
    <>
      <style>{`
        [data-sonner-toaster] [data-type="error"] {
          background-color: #fef2f2 !important;
          border-color: #fecaca !important;
          color: #7f1d1d !important;
        }
        [data-sonner-toaster] [data-type="success"] {
          background-color: #f0fdf4 !important;
          border-color: #bbf7d0 !important;
          color: #14532d !important;
        }
        [data-sonner-toaster] [data-type="error"] [data-description] {
          color: #991b1b !important;
        }
        [data-sonner-toaster] [data-type="success"] [data-description] {
          color: #166534 !important;
        }
      `}</style>
      <Sonner
        theme="light"
        className="group toaster"
        toastOptions={{
          style: {
            border: "1px solid #e5e7eb",
            borderRadius: "8px",
          },
          classNames: {
            toast: "group toast bg-white text-gray-900 border-gray-200 shadow-lg",
            description: "text-gray-600",
            actionButton: "rounded-md bg-blue-600 text-white",
            cancelButton: "rounded-md bg-gray-200 text-gray-900",
          },
        }}
        {...props}
      />
    </>
  );
};

export { Toaster };
