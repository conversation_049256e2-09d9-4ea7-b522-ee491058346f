import type { BillingAddress, PaymentInfo } from "../types/payment";

/**
 * Format amount as currency string
 */
export const formatCurrency = (amount: number, currency = "USD"): string => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency,
    minimumFractionDigits: 2,
  }).format(amount / 100);
};

/**
 * Format date in human-readable format
 */
export const formatDate = (date = new Date()): string => {
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

/**
 * Generate a random reference number
 */
export const generateReferenceNumber = (prefix = "TXN"): string => {
  return `${prefix}-${Math.floor(Math.random() * 10000)}`;
};

/**
 * Generate a random order number
 */
export const generateOrderNumber = (prefix = "ORD"): string => {
  return `${prefix}-${Math.floor(Math.random() * 10000)}`;
};

/**
 * Calculate subtotal (amount minus tax)
 */
export const calculateSubtotal = (amount: number, taxAmount = 0): number => {
  return amount - taxAmount;
};

/**
 * Validate billing address completeness
 */
export const validateBillingAddress = (
  billingAddress: BillingAddress,
  termsAccepted: boolean
): boolean => {
  return (
    billingAddress.firstName.trim() !== "" &&
    billingAddress.lastName.trim() !== "" &&
    billingAddress.email.trim() !== "" &&
    billingAddress.line1.trim() !== "" &&
    billingAddress.city.trim() !== "" &&
    billingAddress.state.trim() !== "" &&
    billingAddress.zip.trim() !== "" &&
    termsAccepted
  );
};

/**
 * Get display reference number from payment info
 */
export const getDisplayReference = (paymentInfo: PaymentInfo): string => {
  return paymentInfo.orderNumber || generateReferenceNumber();
};

/**
 * Get display order number from payment info
 */
export const getDisplayOrderNumber = (paymentInfo: PaymentInfo): string => {
  return paymentInfo.orderNumber || generateOrderNumber();
};

/**
 * Configure iframe-specific body styling
 */
export const configureIframeBodyStyles = (): void => {
  document.body.style.margin = "0";
  document.body.style.padding = "0";
  document.body.style.backgroundColor = "#f8fafc";
  document.body.style.fontFamily = "Inter, system-ui, -apple-system, sans-serif";
};

/**
 * Configure viewport for iframe
 */
export const configureIframeViewport = (): void => {
  const viewport = document.querySelector('meta[name="viewport"]');
  if (viewport) {
    viewport.setAttribute("content", "width=device-width, initial-scale=1.0, user-scalable=no");
  }
};

/**
 * Format payment success message data for parent window
 */
export const formatSuccessMessage = (response: unknown, type: string, data?: unknown) => {
  return {
    type,
    data: data || response,
  };
};

/**
 * Format payment error message for parent window
 */
export const formatErrorMessage = (error: unknown, type: string) => {
  const errorMessage = 
    error instanceof Error 
      ? error.message 
      : (error as { message?: string })?.message || "An error occurred";
  
  return {
    type,
    error: errorMessage,
    details: error,
  };
};