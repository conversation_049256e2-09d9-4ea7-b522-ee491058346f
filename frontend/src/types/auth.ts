export const UserRole = {
  ADMIN: "admin",
  USER: "user",
  SUPER_ADMIN: "super_admin",
  MERCHANT_ADMIN: "merchant_admin",
  MERCHANT_USER: "merchant_user",
  MERCHANT_VIEWER: "merchant_viewer",
} as const;

export type UserRole = (typeof UserRole)[keyof typeof UserRole];

export const UserType = {
  PLATFORM_ADMIN: "platform_admin",
  MERCHANT_USER: "merchant_user",
} as const;

export type UserType = (typeof UserType)[keyof typeof UserType];

export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  userType: UserType;
  merchantId?: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export type RouteAccessLevel = "public" | "authenticated" | "platform_admin" | "merchant_user";
