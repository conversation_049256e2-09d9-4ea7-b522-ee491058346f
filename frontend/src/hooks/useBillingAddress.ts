import { useState } from "react";
import type { BillingAddress } from "../types/payment";
import { validateBillingAddress } from "../utils/paymentUtils";

interface UseBillingAddressReturn {
  billingAddress: BillingAddress;
  termsAccepted: boolean;
  setBillingAddress: React.Dispatch<React.SetStateAction<BillingAddress>>;
  setTermsAccepted: React.Dispatch<React.SetStateAction<boolean>>;
  handleAddressChange: (field: keyof BillingAddress, value: string) => void;
  isAddressValid: () => boolean;
}

/**
 * Custom hook for managing billing address state and validation
 */
export const useBillingAddress = (): UseBillingAddressReturn => {
  const [billingAddress, setBillingAddress] = useState<BillingAddress>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    line1: "",
    line2: "",
    city: "",
    state: "",
    zip: "",
    country: "US",
  });
  
  const [termsAccepted, setTermsAccepted] = useState(false);

  const handleAddressChange = (field: keyof BillingAddress, value: string) => {
    setBillingAddress((prev) => ({ ...prev, [field]: value }));
  };

  const isAddressValid = () => {
    return validateBillingAddress(billingAddress, termsAccepted);
  };

  return {
    billingAddress,
    termsAccepted,
    setBillingAddress,
    setTermsAccepted,
    handleAddressChange,
    isAddressValid,
  };
};